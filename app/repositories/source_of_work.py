import logging
from typing import Any

from config import settings
from core.http_client import CustomAsyncClient
from core.urls import url_join


__all__ = ['SourceOfWorkRepository']

logger = logging.getLogger(__name__)


class SourceOfWorkRepository:
    """Repository for Source of Work API operations."""

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the Source of Work Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
        """
        self._http_client = http_client
        self._base_path = str(settings.project_api.base_url)

    async def list(self, token: str) -> list[dict[str, Any]]:
        """
        List all source of works.

        Returns:
            list[dict[str, Any]]: A list of source of works
        """
        url = url_join(self._base_path, 'source-of-work')
        headers = {'Authorization': f'Bearer {token}'}
        try:
            return (await self._http_client.get(url, headers=headers)).json()

        except Exception as e:
            logger.error('Error listing source of works: %s', e)
            raise e
