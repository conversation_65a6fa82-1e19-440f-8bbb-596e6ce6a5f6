"""Concatenation strategy for descriptive fields."""

from constants.extracted_data import QualUsage
from schemas import ExtractedData

from .base import BaseAggregationStrategy


class ConcatenationStrategy(BaseAggregationStrategy):
    """Strategy for concatenating descriptive fields with separators and duplicate removal."""

    def __init__(self):
        # Engagement Description fields
        self._titles: list[str] = []
        self._business_issues: list[str] = []
        self._scope_approach: list[str] = []
        self._value_delivered: list[str] = []
        self._engagement_summary: list[str] = []
        self._one_line_description: list[str] = []

        # Engagement Details fields
        self._client_references: list[str] = []
        self._client_name_sharing: list[str] = []

        # Usage & Team fields
        self._qual_usage: list[str] = []
        self._team_roles: list[str] = []
        self._approver: list[str] = []

    def process(self, extracted_data: ExtractedData) -> None:
        """Process concatenation fields from extracted data."""
        # Engagement Description fields
        if extracted_data.title:
            self._add_unique_content(self._titles, extracted_data.title)
        if extracted_data.business_issues:
            self._add_unique_content(self._business_issues, extracted_data.business_issues)
        if extracted_data.scope_approach:
            self._add_unique_content(self._scope_approach, extracted_data.scope_approach)
        if extracted_data.value_delivered:
            self._add_unique_content(self._value_delivered, extracted_data.value_delivered)
        if extracted_data.engagement_summary:
            self._add_unique_content(self._engagement_summary, extracted_data.engagement_summary)
        if extracted_data.one_line_description:
            self._add_unique_content(self._one_line_description, extracted_data.one_line_description)

        # Engagement Details fields
        if extracted_data.client_references:
            self._add_unique_content(self._client_references, extracted_data.client_references)
        if extracted_data.client_name_sharing:
            self._add_unique_content(self._client_name_sharing, extracted_data.client_name_sharing)

        # Usage & Team fields
        if extracted_data.qual_usage:
            self._add_unique_content(self._qual_usage, extracted_data.qual_usage)
        if extracted_data.team_roles:
            self._add_unique_content(self._team_roles, extracted_data.team_roles)
        if extracted_data.approver:
            self._add_unique_content(self._approver, extracted_data.approver)

    def _add_unique_content(self, target_list: list[str], content: str) -> None:
        """Add content to list if not already present (case-insensitive)."""
        content = content.strip()
        if content and content.lower() not in [item.lower() for item in target_list]:
            target_list.append(content)

    def get_titles(self) -> str | None:
        return self._join_with_separator(self._titles)

    def get_business_issues(self) -> str | None:
        """Get concatenated business issues."""
        return self._join_with_separator(self._business_issues)

    def get_scope_approach(self) -> str | None:
        """Get concatenated scope approach."""
        return self._join_with_separator(self._scope_approach)

    def get_value_delivered(self) -> str | None:
        """Get concatenated value delivered."""
        return self._join_with_separator(self._value_delivered)

    def get_engagement_summary(self) -> str | None:
        """Get concatenated engagement summary."""
        return self._join_with_separator(self._engagement_summary)

    def get_one_line_description(self) -> str | None:
        """Get concatenated one line description."""
        return self._join_with_separator(self._one_line_description)

    def get_client_references(self) -> str | None:
        """Get concatenated client references."""
        return self._join_with_separator(self._client_references)

    def get_client_name_sharing(self) -> str | None:
        """Get concatenated client name sharing."""
        return self._join_with_separator(self._client_name_sharing)

    def get_qual_usage(self) -> str | None:
        """Get qual usage with proper enum validation and description."""
        if not self._qual_usage:
            return None

        if len(self._qual_usage) == 1:
            key = self._qual_usage[0]
            if not QualUsage.is_valid(key):
                valid_keys = QualUsage.values()
                raise ValueError(f"Unknown qual usage value: '{key}'. Expected one of: {valid_keys}")
            return QualUsage.get_description(key)

        # Multiple values found - default to approval for safety
        return QualUsage.get_description('approval')

    def get_team_roles(self) -> str | None:
        """Get concatenated team roles."""
        return self._join_with_separator(self._team_roles)

    def get_approver(self) -> str | None:
        """Get concatenated approver."""
        return self._join_with_separator(self._approver)

    def _join_with_separator(self, items: list[str]) -> str | None:
        """Join items with appropriate separator, return None if empty."""
        if not items:
            return None
        return ' | '.join(items)
