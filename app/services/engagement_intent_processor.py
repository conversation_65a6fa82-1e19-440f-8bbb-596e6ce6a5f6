from constants.engagement import EngagementMessageIntention, engagement_templates
from constants.message import SystemReplyType
from schemas import EngagementIntentProcessorResponse


__all__ = ['EngagementIntentProcessor']


class EngagementIntentProcessor:
    def process_intent(self, intent: EngagementMessageIntention) -> EngagementIntentProcessorResponse:
        """Process the engagement intent and return a response.
        Args:
            intent (EngagementMessageIntention): The intent to process.
        Returns:
            EngagementIntentProcessorResponse: A Pydantic model containing the processed result.
        """
        mapped_response = {
            EngagementMessageIntention.BUSINESS_ISSUES: engagement_templates.general.edited_text_field_reply,
            EngagementMessageIntention.SCOPE_APPROACH: engagement_templates.general.edited_text_field_reply,
            EngagementMessageIntention.VALUE_DELIVERED_IMPACT: engagement_templates.general.edited_text_field_reply,
            EngagementMessageIntention.ENGAGEMENT_SUMMARY: engagement_templates.general.edited_text_field_reply,
            EngagementMessageIntention.ONE_LINE_DESCRIPTION: engagement_templates.general.edited_text_field_reply,
            EngagementMessageIntention.NAVIGATE_TO_RESOURCE_PAGE: engagement_templates.general.kx_prompt_reply,
            EngagementMessageIntention.UNDEFINED: engagement_templates.general.undefined_reply,
            EngagementMessageIntention.UNKNOWN_FIELD: engagement_templates.general.undefined_reply,
        }
        if intent in mapped_response:
            return EngagementIntentProcessorResponse(response=mapped_response[intent])
        else:
            return EngagementIntentProcessorResponse(response=engagement_templates.general.other_page_reply)

    def process_intent_reply_type(self, intent: EngagementMessageIntention) -> SystemReplyType:
        """Process the engagement intent and return a system reply type.
        Args:
            intent (EngagementMessageIntention): The intent to process.
        Returns:
            SystemReplyType: The system reply type.
        """
        mapped_reply_type = {
            EngagementMessageIntention.BUSINESS_ISSUES: SystemReplyType.ENGAGEMENT_DETAILS_FIELD_MODIFIED,
            EngagementMessageIntention.SCOPE_APPROACH: SystemReplyType.ENGAGEMENT_DETAILS_FIELD_MODIFIED,
            EngagementMessageIntention.VALUE_DELIVERED_IMPACT: SystemReplyType.ENGAGEMENT_DETAILS_FIELD_MODIFIED,
            EngagementMessageIntention.ENGAGEMENT_SUMMARY: SystemReplyType.ENGAGEMENT_DETAILS_FIELD_MODIFIED,
            EngagementMessageIntention.ONE_LINE_DESCRIPTION: SystemReplyType.ENGAGEMENT_DETAILS_FIELD_MODIFIED,
            EngagementMessageIntention.NAVIGATE_TO_RESOURCE_PAGE: SystemReplyType.ENGAGEMENT_DETAILS_NAVIGATED,
            EngagementMessageIntention.UNDEFINED: SystemReplyType.ENGAGEMENT_DETAILS_UNDEFINED,
            EngagementMessageIntention.UNKNOWN_FIELD: SystemReplyType.ENGAGEMENT_DETAILS_UNKNOWN_FIELD,
        }
        if intent in mapped_reply_type:
            return mapped_reply_type[intent]
        else:
            return SystemReplyType.ENGAGEMENT_DETAILS_UNDEFINED
