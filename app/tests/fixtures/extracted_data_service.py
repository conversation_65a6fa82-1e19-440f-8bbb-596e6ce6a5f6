import asyncio
from datetime import date, datetime, timezone
import json
from unittest.mock import AsyncMock
from uuid import UUID

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from constants.extracted_data import DataSourceType
from repositories import (
    ConversationRepository,
    ExtractedDataRepository,
    QualsClientsRepository,
)
from schemas import ExtractedData, IndustryData, ServiceData
from schemas.project_roles import ProjectRolesData
from services import ExtractedDataService


__all__ = [
    'extracted_data_repository',
    'extracted_data_service',
    'kx_dash_extracted_data',
    'documents_extracted_data',
    'prompt_extracted_data',
    'extracted_data_repository_real',
    'extracted_data_service_real',
    'industry_data_service',
    'role_data_service',
    'service_data_service',
    'user_info_service',
    'extracted_data_repository_real_with_autocommit',
    'ldmf_country_service',
]


@pytest.fixture
def industry_data_service():
    """Mock industry data service"""
    return AsyncMock()


@pytest.fixture
def role_data_service():
    """Mock role data service"""
    mock_service = AsyncMock()
    # Configure the list method to return proper ProjectRolesData objects
    mock_service.list.return_value = {
        'Team Member': ProjectRolesData(id=1, title='Team Member', order=1, name='Team Member'),
        'Manager': ProjectRolesData(id=2, title='Manager', order=2, name='Manager'),
    }
    return mock_service


@pytest.fixture
def service_data_service():
    """Mock service data service"""
    return AsyncMock()


@pytest.fixture
def user_info_service():
    """Mock user info service"""
    return AsyncMock()


@pytest.fixture
def extracted_data_repository():
    return AsyncMock()


@pytest.fixture
def ldmf_country_service():
    """Mock LDMF country service with blob repository"""
    mock = AsyncMock()
    mock.list.return_value = []
    mock.verify_ldmf_country.return_value = None
    # Mock the blob repository
    mock.blob_repository = AsyncMock()
    mock.blob_repository.upload.return_value = None
    return mock


@pytest.fixture
def extracted_data_service(
    extracted_data_repository,
    industry_data_service,
    role_data_service,
    service_data_service,
    user_info_service,
    ldmf_country_service,
):
    conversation_repository = AsyncMock()
    quals_clients_repository = AsyncMock()
    source_of_work_repository = AsyncMock()
    return ExtractedDataService(
        extracted_data_repository=extracted_data_repository,
        conversation_repository=conversation_repository,
        quals_clients_repository=quals_clients_repository,
        source_of_work_repository=source_of_work_repository,
        industry_data_service=industry_data_service,
        role_data_service=role_data_service,
        fee_and_currency_service=service_data_service,
        user_info_service=user_info_service,
        ldmf_country_service=ldmf_country_service,
        service_data_service=service_data_service,
    )


@pytest.fixture
def kx_dash_extracted_data():
    return ExtractedData(
        ConversationPublicId=UUID('00000000-0000-0000-0000-000000000001'),
        DataSourceType=DataSourceType.KX_DASH,
        CreatedAt=datetime.now(timezone.utc),
        ActivityName='KX Activity',
        ClientName=json.dumps(['KX Client']),  # type: ignore
        LDMFCountry=json.dumps(['KX Country']),  # type: ignore
        Title='KX Title',
        StartDate=date(2023, 1, 1),
        EndDate=date(2023, 12, 31),
        ClientIndustries=[IndustryData(name='Test name', ldmf_country='Test country', is_primary=True)],
        ClientServices=[ServiceData(name='Test name', is_primary=True)],
        Roles=json.dumps(
            [
                {
                    'name': 'KX Team Member',
                    'email': '<EMAIL>',
                    'roles': ['Team Member'],
                    'is_approver': False,
                    'is_contact': False,
                    'duration': 12,
                }
            ]
        ),
        ObjectiveAndScope='KX Objective and Scope',
        Outcomes='KX Outcomes',
    )


@pytest.fixture
def documents_extracted_data():
    return ExtractedData(
        ConversationPublicId=UUID('00000000-0000-0000-0000-000000000001'),
        DataSourceType=DataSourceType.DOCUMENTS,
        CreatedAt=datetime.now(timezone.utc),
        ActivityName='Document Activity',
        ClientName=json.dumps(['Document Client']),  # type: ignore
        LDMFCountry=json.dumps(['Document Country']),  # type: ignore
        Title='Document Title',
        StartDate=date(2023, 2, 1),
        EndDate=date(2023, 11, 30),
        ClientIndustries=[IndustryData(name='Test name', ldmf_country='Test country', is_primary=True)],
        ClientServices=[ServiceData(name='Test name', is_primary=True)],
        Roles=json.dumps(
            [
                {
                    'name': 'Document Team Member',
                    'email': '<EMAIL>',
                    'roles': ['Team Member'],
                    'is_approver': False,
                    'is_contact': False,
                    'duration': 12,
                }
            ]
        ),
        ObjectiveAndScope='Document Objective and Scope',
        Outcomes='Document Outcomes',
        Other='Document Other',
    )


@pytest.fixture
def prompt_extracted_data():
    return ExtractedData(
        ConversationPublicId=UUID('00000000-0000-0000-0000-000000000001'),
        DataSourceType=DataSourceType.PROMPT,
        CreatedAt=datetime.now(timezone.utc),
        ActivityName='Prompt Activity',
        ClientName=json.dumps(['Prompt Client']),  # type: ignore
        LDMFCountry=json.dumps(['Prompt Country']),  # type: ignore
        Title='Prompt Title',
        StartDate=date(2023, 3, 1),
        EndDate=date(2023, 10, 31),
        ClientIndustries=[IndustryData(name='Test name', ldmf_country='Test country', is_primary=True)],
        ClientServices=[ServiceData(name='Test name', is_primary=True)],
        Roles=json.dumps(
            [
                {
                    'name': 'Prompt Team Member',
                    'email': '<EMAIL>',
                    'roles': ['Team Member'],
                    'is_approver': False,
                    'is_contact': False,
                    'duration': 12,
                }
            ]
        ),
        ObjectiveAndScope='Prompt Objective and Scope',
        Outcomes='Prompt Outcomes',
        Other='Prompt Other',
    )


class AutoCommitRepository:
    def __init__(self, db_repo, db_session):
        self._db_repo = db_repo
        self._db_session = db_session

    def __getattr__(self, name):
        """Handle any method call dynamically."""
        method = getattr(self._db_repo, name)
        assert asyncio.iscoroutinefunction(method), f'Method {name} is not callable'

        async def wrapper(*args, **kwargs):
            result = await method(*args, **kwargs)
            await self._db_session.commit()
            return result

        return wrapper


@pytest.fixture
async def extracted_data_repository_real_with_autocommit(db_engine):
    """Fixture providing ExtractedDataRepository with its own session."""

    async with AsyncSession(db_engine) as session:
        conversation_repository = ConversationRepository(db_session=session)
        base_repository = ExtractedDataRepository(db_session=session, conversation_repository=conversation_repository)
        yield AutoCommitRepository(base_repository, session)


@pytest.fixture
def extracted_data_repository_real(db_session):
    conversation_repository = ConversationRepository(db_session=db_session)
    return ExtractedDataRepository(db_session=db_session, conversation_repository=conversation_repository)


@pytest.fixture
def extracted_data_service_real(extracted_data_repository_real, db_session) -> ExtractedDataService:
    conversation_repository = ConversationRepository(db_session=db_session)
    quals_clients_repository = QualsClientsRepository(http_client=AsyncMock(), cache_repository=AsyncMock())
    ldmf_country_service = AsyncMock()
    ldmf_country_service.list.return_value = []
    # Mock verify_ldmf_country to return None for all countries (indicating they fail verification)
    ldmf_country_service.verify_ldmf_country.return_value = None

    # Mock role_data_service to return ProjectRolesData objects
    role_data_service = AsyncMock()
    role_data_service.list.return_value = {
        'Lead Engagement Partner': ProjectRolesData(
            id=1, title='Lead Engagement Partner', order=1, name='Lead Engagement Partner'
        ),
        'LCSP': ProjectRolesData(id=2, title='LCSP', order=2, name='LCSP'),
        'Team Member': ProjectRolesData(id=3, title='Team Member', order=3, name='Team Member'),
    }

    return ExtractedDataService(
        extracted_data_repository=extracted_data_repository_real,
        conversation_repository=conversation_repository,
        quals_clients_repository=quals_clients_repository,
        source_of_work_repository=AsyncMock(),
        industry_data_service=AsyncMock(),
        role_data_service=role_data_service,
        fee_and_currency_service=AsyncMock(),
        user_info_service=AsyncMock(),
        ldmf_country_service=ldmf_country_service,
        service_data_service=AsyncMock(),
    )
