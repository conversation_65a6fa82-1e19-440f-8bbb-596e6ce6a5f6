"""
Test for the extracted data repository update logic fix.

This test verifies that partial extractions don't overwrite existing complete data.
"""

from datetime import date

from constants.extracted_data import DataSourceType
from schemas import ExtractedData


class TestExtractedDataRepositoryUpdateFix:
    """Test the fix for partial extraction overwriting existing data."""

    async def test_partial_extraction_preserves_existing_data(
        self,
        test_conversation_id,
        extracted_data_repository_real,
    ):
        """Test that partial extraction doesn't overwrite existing complete data."""

        # Step 1: Create initial complete extraction
        initial_data: ExtractedData = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.DOCUMENTS
        )
        initial_data.client_name = ['Complete Client Name']
        initial_data.ldmf_country = ['Complete Country']
        initial_data.start_date = date(2023, 1, 1)
        initial_data.end_date = date(2023, 12, 31)
        initial_data.objective_and_scope = 'Complete objective and scope'
        initial_data.outcomes = 'Complete outcomes'
        initial_data.title = 'Complete Title'

        # Save initial complete data
        await extracted_data_repository_real.update(initial_data)

        # Verify initial data was saved correctly
        retrieved_initial: ExtractedData = await extracted_data_repository_real.get(
            test_conversation_id, DataSourceType.DOCUMENTS
        )
        assert retrieved_initial is not None
        assert retrieved_initial.client_name == ['Complete Client Name']
        assert retrieved_initial.ldmf_country == ['Complete Country']
        assert retrieved_initial.start_date == date(2023, 1, 1)
        assert retrieved_initial.end_date == date(2023, 12, 31)
        assert retrieved_initial.objective_and_scope == 'Complete objective and scope'
        assert retrieved_initial.outcomes == 'Complete outcomes'
        assert retrieved_initial.title == 'Complete Title'

        # Step 2: Create partial extraction with only client name
        partial_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.DOCUMENTS
        )
        partial_data.client_name = ['New Client Name']
        # All other fields remain empty/default

        # Save partial data (this should NOT overwrite existing fields)
        await extracted_data_repository_real.update(partial_data)

        # Step 3: Verify that existing data is preserved
        retrieved_after_partial = await extracted_data_repository_real.get(
            test_conversation_id, DataSourceType.DOCUMENTS
        )
        assert retrieved_after_partial is not None

        # Client name should be updated (new meaningful value)
        assert retrieved_after_partial.client_name == ['New Client Name']

        # All other fields should be preserved from initial extraction
        assert retrieved_after_partial.ldmf_country == ['Complete Country']
        assert retrieved_after_partial.start_date == date(2023, 1, 1)
        assert retrieved_after_partial.end_date == date(2023, 12, 31)
        assert retrieved_after_partial.objective_and_scope == 'Complete objective and scope'
        assert retrieved_after_partial.outcomes == 'Complete outcomes'
        assert retrieved_after_partial.title == 'Complete Title'

    async def test_empty_list_fields_dont_overwrite_existing(
        self,
        test_conversation_id,
        extracted_data_repository_real,
    ):
        """Test that empty list fields don't overwrite existing populated lists."""

        # Step 1: Create initial data with populated lists
        initial_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.PROMPT
        )
        initial_data.client_name = ['Existing Client']
        initial_data.ldmf_country = ['Existing Country']

        await extracted_data_repository_real.update(initial_data)

        # Step 2: Create partial data with empty lists
        partial_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.PROMPT
        )
        # client_name and ldmf_country will be empty lists by default
        partial_data.title = 'New Title'  # Only update title

        await extracted_data_repository_real.update(partial_data)

        # Step 3: Verify lists are preserved
        retrieved = await extracted_data_repository_real.get(test_conversation_id, DataSourceType.PROMPT)
        assert retrieved is not None
        assert retrieved.client_name == ['Existing Client']  # Should be preserved
        assert retrieved.ldmf_country == ['Existing Country']  # Should be preserved
        assert retrieved.title == 'New Title'  # Should be updated

    async def test_empty_string_fields_dont_overwrite_existing(
        self,
        test_conversation_id,
        extracted_data_repository_real,
    ):
        """Test that empty string fields don't overwrite existing populated strings."""

        # Step 1: Create initial data with populated strings
        initial_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        initial_data.objective_and_scope = 'Existing objective'
        initial_data.outcomes = 'Existing outcomes'
        initial_data.title = 'Existing title'

        await extracted_data_repository_real.update(initial_data)

        # Step 2: Create partial data with empty strings
        partial_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        partial_data.client_name = ['New Client']  # Only update client name
        # objective_and_scope, outcomes, title remain None/empty

        await extracted_data_repository_real.update(partial_data)

        # Step 3: Verify strings are preserved
        retrieved = await extracted_data_repository_real.get(test_conversation_id, DataSourceType.KX_DASH)
        assert retrieved is not None
        assert retrieved.client_name == ['New Client']  # Should be updated
        assert retrieved.objective_and_scope == 'Existing objective'  # Should be preserved
        assert retrieved.outcomes == 'Existing outcomes'  # Should be preserved
        assert retrieved.title == 'Existing title'  # Should be preserved

    async def test_none_dates_dont_overwrite_existing_dates(
        self,
        test_conversation_id,
        extracted_data_repository_real,
    ):
        """Test that None date values don't overwrite existing dates."""

        # Step 1: Create initial data with dates
        initial_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.DOCUMENTS
        )
        initial_data.start_date = date(2023, 6, 1)
        initial_data.end_date = date(2023, 12, 31)
        initial_data.client_name = ['Client with dates']

        await extracted_data_repository_real.update(initial_data)

        # Step 2: Create partial data without dates
        partial_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.DOCUMENTS
        )
        partial_data.outcomes = 'New outcomes'  # Only update outcomes
        # start_date and end_date remain None

        await extracted_data_repository_real.update(partial_data)

        # Step 3: Verify dates are preserved
        retrieved = await extracted_data_repository_real.get(test_conversation_id, DataSourceType.DOCUMENTS)
        assert retrieved is not None
        assert retrieved.start_date == date(2023, 6, 1)  # Should be preserved
        assert retrieved.end_date == date(2023, 12, 31)  # Should be preserved
        assert retrieved.outcomes == 'New outcomes'  # Should be updated
        assert retrieved.client_name == ['Client with dates']  # Should be preserved

    async def test_meaningful_values_still_update_existing(
        self,
        test_conversation_id,
        extracted_data_repository_real,
    ):
        """Test that meaningful new values still update existing data."""

        # Step 1: Create initial data
        initial_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.PROMPT
        )
        initial_data.client_name = ['Old Client']
        initial_data.outcomes = 'Old outcomes'

        await extracted_data_repository_real.update(initial_data)

        # Step 2: Create update with meaningful new values
        update_data = ExtractedData.create(conversation_id=test_conversation_id, data_source_type=DataSourceType.PROMPT)
        update_data.client_name = ['New Client']  # Meaningful update
        update_data.outcomes = 'New outcomes'  # Meaningful update

        await extracted_data_repository_real.update(update_data)

        # Step 3: Verify meaningful values were updated
        retrieved = await extracted_data_repository_real.get(test_conversation_id, DataSourceType.PROMPT)
        assert retrieved is not None
        assert retrieved.client_name == ['New Client']  # Should be updated
        assert retrieved.outcomes == 'New outcomes'  # Should be updated
