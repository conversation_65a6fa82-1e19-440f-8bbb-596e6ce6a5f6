from unittest.mock import AsyncMock
from uuid import uuid4

import pytest

from constants.extracted_data import ConversationState, DataSourceType, FieldStatus, MissingDataStatus
from constants.message import NEED_INFO_LDMF_COUNTRY, SystemReplyType
from schemas import AggregatedData, ExtractedData
from schemas.confirmed_data import ConfirmedData
from services.extracted_data.handlers import LDMFCountryHandler


class TestLDMFCountryHandlerUnit:
    """Unit tests for LDMFCountryHandler specific functionality."""

    @pytest.fixture
    def ldmf_country_service(self):
        """Fixture providing mock LDMF country service with test data."""
        mock = AsyncMock()
        mock.list.return_value = [
            {'name': 'Test Country'},
            {'name': 'Another Country'},
            {'name': 'KX Dash Country'},
            {'name': 'Documents Country'},
        ]

        # Mock verify_ldmf_country to return None for all countries
        # This simulates that extracted countries are not found in the API/database
        # and need user confirmation
        mock.verify_ldmf_country.return_value = None
        return mock

    @pytest.fixture
    def country_handler(self, ldmf_country_service):
        """Fixture providing LDMFCountryHandler instance."""
        return LDMFCountryHandler(ldmf_country_service)

    @pytest.fixture
    def empty_aggregated_data(self):
        """Fixture providing empty AggregatedData."""
        return AggregatedData()

    @pytest.fixture
    def aggregated_data_with_country(self):
        """Fixture providing AggregatedData with country."""
        return AggregatedData(ldmf_country=['Test Country'])

    @pytest.fixture
    def empty_confirmed_data(self):
        """Fixture providing empty ConfirmedData."""
        return ConfirmedData()

    @pytest.fixture
    def confirmed_data_with_country(self):
        """Fixture providing ConfirmedData with country."""
        return ConfirmedData(ldmf_country='Confirmed Test Country')

    async def test_country_already_confirmed(self, country_handler, empty_aggregated_data, confirmed_data_with_country):
        """Test when country is already confirmed - should return CONFIRMED status."""
        response = await country_handler.check_and_get_response(
            aggregated_data=empty_aggregated_data, confirmed_data=confirmed_data_with_country, token='test_token'
        )

        assert not response.needs_confirmation
        assert response.system_message is None
        assert response.next_expected_field is None
        assert response.field_status == FieldStatus.CONFIRMED

    async def test_country_in_aggregated_data_not_confirmed(
        self, country_handler, aggregated_data_with_country, empty_confirmed_data
    ):
        """Test when country exists in aggregated_data but not confirmed - should return MISSING status."""
        response = await country_handler.check_and_get_response(
            aggregated_data=aggregated_data_with_country, confirmed_data=empty_confirmed_data, token='test_token'
        )

        assert response.needs_confirmation
        assert response.field_status == FieldStatus.SINGLE
        assert response.system_message == SystemReplyType.EXTRACTED_LDMF_NOT_VALID.message_text
        assert response.next_expected_field is None

    async def test_country_missing_entirely(self, country_handler, empty_aggregated_data, empty_confirmed_data):
        """Test when country is missing entirely - should return MISSING status."""
        response = await country_handler.check_and_get_response(
            aggregated_data=empty_aggregated_data, confirmed_data=empty_confirmed_data, token='test_token'
        )

        assert response.needs_confirmation
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_LDMF_COUNTRY
        assert response.next_expected_field is None

    @pytest.mark.asyncio
    async def test_confirmed_data_overrides_aggregated_data(
        self, country_handler, aggregated_data_with_country, confirmed_data_with_country
    ):
        """Test that confirmed data takes precedence over aggregated data."""
        response = await country_handler.check_and_get_response(
            aggregated_data=aggregated_data_with_country, confirmed_data=confirmed_data_with_country, token='test_token'
        )

        # Should return CONFIRMED status even though aggregated data also has country
        assert not response.needs_confirmation
        assert response.system_message is None
        assert response.next_expected_field is None
        assert response.field_status == FieldStatus.CONFIRMED


class TestExtractedDataServiceCountryIntegration:
    """Integration tests for ExtractedDataService with real database connections."""

    @pytest.mark.asyncio
    async def test_missing_data_detection_country_missing(
        self,
        test_conversation_id,
        extracted_data_service_real,
    ):
        """Test missing data detection when country is completely missing."""
        # Test with no extracted data at all
        confirmed_data = ConfirmedData()

        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should detect missing client info first (based on field collection order)
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_CLIENT_NAME
        assert response.message is not None
        assert len(response.missing_fields) > 0

    async def test_missing_data_detection_country_in_aggregated_data(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real,
    ):
        """Test missing data detection when country exists in aggregated data but needs confirmation."""
        # Create extracted data with country
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        extracted_data.ldmf_country = ['Test Country']
        extracted_data.client_name = ['Test Client']

        # Save to database
        await extracted_data_repository_real.update(extracted_data)

        # Test with confirmed data for client name but not country
        confirmed_data = ConfirmedData(
            client_name='Test Client',
            # country intentionally left None
        )

        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should ask for country confirmation even with single value for accuracy
        assert response.status == MissingDataStatus.EXTRACTED_DATA_NOT_VALID
        assert response.conversation_state == ConversationState.COLLECTING_COUNTRY

    async def test_missing_data_detection_country_confirmed(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real,
    ):
        """Test missing data detection when country is confirmed."""
        # Create extracted data with country
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        extracted_data.ldmf_country = ['Test Country']
        extracted_data.client_name = ['Test Client']

        # Save to database
        await extracted_data_repository_real.update(extracted_data)

        # Test with all fields confirmed including country
        confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='Test Country',
        )

        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should move to next field (dates)
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_DATES


class TestThreeScenarioCountryHandling:
    """Test the three-scenario handling pattern (zero/one/multiple values) for country field."""

    async def test_zero_values_scenario(
        self,
        test_conversation_id,
        extracted_data_service_real,
    ):
        """Test scenario with zero country values."""
        # No extracted data created - zero values scenario
        confirmed_data = ConfirmedData(
            client_name='Test Client',
            # country left None
        )

        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should detect missing country
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_COUNTRY
        assert response.message == NEED_INFO_LDMF_COUNTRY

    async def test_one_value_scenario(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real,
    ):
        """Test scenario with one country value."""
        # Create extracted data with single country value
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        extracted_data.ldmf_country = ['Single Country Value']
        extracted_data.client_name = ['Test Client']

        # Save to database
        await extracted_data_repository_real.update(extracted_data)

        confirmed_data = ConfirmedData(
            client_name='Test Client',
            # country left None for confirmation
        )

        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should ask for country confirmation even with single value for accuracy
        assert response.status == MissingDataStatus.EXTRACTED_DATA_NOT_VALID
        assert response.conversation_state == ConversationState.COLLECTING_COUNTRY

    async def test_multiple_values_scenario(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real,
    ):
        """Test scenario with multiple country values."""
        # Create extracted data from multiple sources with different countries
        kx_dash_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        kx_dash_data.ldmf_country = ['KX Dash Country']
        kx_dash_data.client_name = ['Test Client']
        await extracted_data_repository_real.update(kx_dash_data)

        documents_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.DOCUMENTS
        )
        documents_data.ldmf_country = ['Documents Country']
        documents_data.client_name = ['Test Client']
        await extracted_data_repository_real.update(documents_data)

        confirmed_data = ConfirmedData(
            client_name='Test Client',
            # country left None for confirmation
        )

        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # When multiple countries fail verification, should ask for country confirmation
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_COUNTRY


class TestErrorHandlingAndEdgeCases:
    """Test error handling and edge cases for country field."""

    async def test_nonexistent_conversation_id(
        self,
        extracted_data_service_real,
    ):
        """Test handling of nonexistent conversation ID."""
        nonexistent_id = uuid4()
        confirmed_data = ConfirmedData()

        # Should not raise exception, should handle gracefully
        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=nonexistent_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should detect missing data (since no extracted data exists)
        assert response.status == MissingDataStatus.ERROR, response
        assert response.conversation_state == ConversationState.INITIAL, response
        assert response.message == 'Conversation not found.'

    async def test_malformed_confirmed_data_json(self):
        """Test handling of malformed confirmed data JSON."""
        # Test with malformed JSON
        malformed_json = '{"client_name": "test", "invalid_json":'
        confirmed_data = ConfirmedData.from_json_string(malformed_json)

        # Should return empty ConfirmedData object
        assert confirmed_data.client_name is None
        assert confirmed_data.ldmf_country is None

        # Test with None
        confirmed_data = ConfirmedData.from_json_string(None)
        assert confirmed_data.client_name is None
        assert confirmed_data.ldmf_country is None

        # Test with empty string
        confirmed_data = ConfirmedData.from_json_string('')
        assert confirmed_data.client_name is None
        assert confirmed_data.ldmf_country is None
