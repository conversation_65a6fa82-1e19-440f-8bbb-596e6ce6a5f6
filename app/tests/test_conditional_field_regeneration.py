import json
from unittest.mock import AsyncMock, patch

from fastapi import status
import pytest

from constants.engagement import EngagementMessageIntention
from constants.message import MessageType, PageType, QualFieldName, TextEditCommand
from constants.operation_ids import operation_ids
from core.http_client import CustomAsyncClient
from core.urls import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from schemas.engagement_message import EngagementMessageIntentClassifierServiceResponse


class TestConditionalFieldRegeneration:
    """Test conditional field regeneration functionality."""

    @pytest.mark.parametrize(
        'command_type,intent,should_regenerate',
        [
            # Should trigger regeneration
            (TextEditCommand.EXPAND, EngagementMessageIntention.BUSINESS_ISSUES, True),
            (TextEditCommand.SHORTEN, EngagementMessageIntention.SCOPE_APPROACH, True),
            (TextEditCommand.REWRITE, EngagementMessageIntention.VALUE_DELIVERED_IMPACT, True),
            (TextEditCommand.PROMPT, EngagementMessageIntention.BUSINESS_ISSUES, True),
            # Should NOT trigger regeneration - wrong intent
            (TextEditCommand.EXPAND, EngagementMessageIntention.ENGAGEMENT_SUMMARY, False),
            (TextEditCommand.EXPAND, EngagementMessageIntention.ONE_LINE_DESCRIPTION, False),
            (TextEditCommand.EXPAND, EngagementMessageIntention.UNDEFINED, False),
            # Should NOT trigger regeneration - wrong command type
            (TextEditCommand.STORE, EngagementMessageIntention.BUSINESS_ISSUES, False),
            (TextEditCommand.UNDO, EngagementMessageIntention.BUSINESS_ISSUES, False),
        ],
    )
    async def test_conditional_regeneration_trigger_conditions(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
        command_type: TextEditCommand,
        intent: EngagementMessageIntention,
        should_regenerate: bool,
    ):
        """Test that conditional regeneration is triggered only under correct conditions."""
        # Setup command data
        command_data = {
            'command': command_type.value,
            'field_name': QualFieldName.CHAT.value,  # Chat commands use CHAT field
            'context': 'Original business context that needs editing',
            'formatted_context': 'Original business context that needs editing',
            'snippet': 'business context',
        }

        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        form_data = {
            'conversation_id': str(test_conversation_id),
            'content': 'Please expand this section with more details',
            'command': json.dumps(command_data),
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        # Mock intent classification to return the specified intent
        mock_intent_response = EngagementMessageIntentClassifierServiceResponse(intention=intent)

        with (
            patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit,
            patch(
                'services.intent_classifier.IntentClassifierService.classify_intent',
                new_callable=AsyncMock,
                return_value=mock_intent_response,
            ),
            patch(
                'services.message_handlers.engagement_description_handler.EngagementDescriptionMessageHandler._regenerate_field_for_conditional',
                new_callable=AsyncMock,
            ) as mock_regenerate,
        ):
            mock_text_edit.return_value = 'EXPANDED business context with additional details'
            mock_regenerate.side_effect = [
                'Regenerated Engagement Summary',
                'Regenerated One-Line Description',
            ]

            response = await async_client.post(message_url, headers=auth_header, data=form_data)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()

        if should_regenerate:
            # Verify regeneration was called
            assert mock_regenerate.call_count == 2
            # Check that regeneration was called with correct field names
            calls = mock_regenerate.call_args_list
            assert len(calls) == 2

            # Extract field names from calls
            called_field_names = {call[1]['field_name'] for call in calls}
            expected_field_names = {QualFieldName.ENGAGEMENT_SUMMARY, QualFieldName.ONE_LINE_DESCRIPTION}
            assert called_field_names == expected_field_names

            # Verify regenerated fields are in response
            system_fields = data['system']['qual_fields']
            assert QualFieldName.ENGAGEMENT_SUMMARY.value in system_fields
            assert QualFieldName.ONE_LINE_DESCRIPTION.value in system_fields
            assert system_fields[QualFieldName.ENGAGEMENT_SUMMARY.value]['context'] == 'Regenerated Engagement Summary'
            assert (
                system_fields[QualFieldName.ONE_LINE_DESCRIPTION.value]['context'] == 'Regenerated One-Line Description'
            )
        else:
            # Verify regeneration was NOT called
            assert mock_regenerate.call_count == 0

    async def test_conditional_regeneration_excludes_undo_operations(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test that conditional regeneration is NOT triggered for undo operations."""
        # Setup undo command data (field_name != CHAT indicates RTE undo)
        command_data = {
            'command': TextEditCommand.UNDO.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,  # RTE undo uses specific field
            'context': 'Previous business context',
            'formatted_context': 'Previous business context',
            'snippet': '',
        }

        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        form_data = {
            'conversation_id': str(test_conversation_id),
            'content': '',
            'command': json.dumps(command_data),
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        # Mock intent classification to return business issues intent
        mock_intent_response = EngagementMessageIntentClassifierServiceResponse(
            intention=EngagementMessageIntention.BUSINESS_ISSUES
        )

        with (
            patch(
                'services.intent_classifier.IntentClassifierService.classify_intent',
                new_callable=AsyncMock,
                return_value=mock_intent_response,
            ),
            patch(
                'services.message_handlers.engagement_description_handler.EngagementDescriptionMessageHandler._regenerate_field_for_conditional',
                new_callable=AsyncMock,
            ) as mock_regenerate,
        ):
            response = await async_client.post(message_url, headers=auth_header, data=form_data)

        assert response.status_code == status.HTTP_201_CREATED

        # Verify regeneration was NOT called for undo operations
        assert mock_regenerate.call_count == 0

    async def test_conditional_regeneration_error_handling(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test that errors in conditional regeneration don't break the main flow."""
        command_data = {
            'command': TextEditCommand.EXPAND.value,
            'field_name': QualFieldName.CHAT.value,
            'context': 'Original business context',
            'formatted_context': 'Original business context',
            'snippet': 'business context',
        }

        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        form_data = {
            'conversation_id': str(test_conversation_id),
            'content': 'Please expand this section',
            'command': json.dumps(command_data),
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        mock_intent_response = EngagementMessageIntentClassifierServiceResponse(
            intention=EngagementMessageIntention.BUSINESS_ISSUES
        )

        with (
            patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit,
            patch(
                'services.intent_classifier.IntentClassifierService.classify_intent',
                new_callable=AsyncMock,
                return_value=mock_intent_response,
            ),
            patch(
                'services.message_handlers.engagement_description_handler.EngagementDescriptionMessageHandler._regenerate_field_for_conditional',
                new_callable=AsyncMock,
                side_effect=Exception('Regeneration failed'),
            ) as mock_regenerate,
        ):
            mock_text_edit.return_value = 'EXPANDED business context'

            # Should not raise exception despite regeneration failure
            response = await async_client.post(message_url, headers=auth_header, data=form_data)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()

        # Main command processing should still work
        assert data['system']['type'] == MessageType.TEXT.value
        assert QualFieldName.CHAT.value not in data['system']['qual_fields']

        # Verify regeneration was attempted but failed gracefully
        assert mock_regenerate.call_count == 2

    async def test_conditional_regeneration_integration_with_existing_fields(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test that conditional regeneration properly merges with existing qual_fields."""
        command_data = {
            'command': TextEditCommand.REWRITE.value,
            'field_name': QualFieldName.CHAT.value,
            'context': 'Rewritten scope and approach content',
            'formatted_context': 'Rewritten scope and approach content',
            'snippet': 'scope and approach',
        }

        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        form_data = {
            'conversation_id': str(test_conversation_id),
            'content': 'Please rewrite this to be more concise',
            'command': json.dumps(command_data),
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        mock_intent_response = EngagementMessageIntentClassifierServiceResponse(
            intention=EngagementMessageIntention.SCOPE_APPROACH
        )

        with (
            patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit,
            patch(
                'services.intent_classifier.IntentClassifierService.classify_intent',
                new_callable=AsyncMock,
                return_value=mock_intent_response,
            ),
            patch(
                'services.message_handlers.engagement_description_handler.EngagementDescriptionMessageHandler._regenerate_field_for_conditional',
                new_callable=AsyncMock,
            ) as mock_regenerate,
        ):
            mock_text_edit.return_value = 'REWRITTEN scope and approach content'
            mock_regenerate.side_effect = [
                'Conditionally Regenerated Summary',
                'Conditionally Regenerated One-Line',
            ]

            response = await async_client.post(message_url, headers=auth_header, data=form_data)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        system_fields = data['system']['qual_fields']

        # Verify all fields are present: original command field + regenerated fields
        assert QualFieldName.CHAT.value not in system_fields
        assert QualFieldName.ENGAGEMENT_SUMMARY.value in system_fields
        assert QualFieldName.ONE_LINE_DESCRIPTION.value in system_fields

        # Verify field contents
        assert system_fields[QualFieldName.ENGAGEMENT_SUMMARY.value]['context'] == 'Conditionally Regenerated Summary'
        assert (
            system_fields[QualFieldName.ONE_LINE_DESCRIPTION.value]['context'] == 'Conditionally Regenerated One-Line'
        )
