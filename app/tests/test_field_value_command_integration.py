"""
Integration tests for command functionality with field values.

This module tests the complete integration between:
- Command processing in field values
- FieldValueRepository
- CommandService and TextEditService
- End-to-end workflow from API to database
"""

import json
from unittest.mock import AsyncMock, patch
from uuid import uuid4

from fastapi import status
import pytest

from constants.extracted_data import DataSourceType
from constants.message import (
    MessageType,
    PageType,
    QualFieldName,
    SystemReplyType,
    TextEditCommand,
)
from constants.operation_ids import operation_ids
from core.http_client import CustomAsyncClient
from core.urls import URLResolver
from schemas import Command, ExtractedData


class TestFieldValueCommandIntegration:
    """Integration tests for command functionality in field values."""

    async def test_command_creates_field_value_record(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test that command processing creates a record in field value repository."""
        # Prepare command data
        command_data = {
            'command': TextEditCommand.EXPAND.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': 'Original business issues that need expansion and clarification.',
            'formatted_context': 'Original business issues that need expansion and clarification.',
            'snippet': 'business issues that need expansion',
        }

        # Mock the TextEditService response
        expected_edited_text = (
            'Original EXPANDED business issues that need expansion and clarification with detailed analysis.'
        )

        with patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit:
            mock_text_edit.return_value = expected_edited_text

            # Create message with command
            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            form_data = {
                'conversation_id': str(test_conversation_id),
                'content': '',
                'command': json.dumps(command_data),
                'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
            }

            response = await async_client.post(message_url, headers=auth_header, data=form_data)

        # Verify response success
        assert response.status_code == status.HTTP_201_CREATED

        # Verify field value was created in database
        # Note: We would need to implement a way to verify this through the repository
        # For now, we verify the API response indicates success
        data = response.json()
        assert data['system']['type'] == str(MessageType.TEXT)
        assert data['system']['system_reply_type'] == str(SystemReplyType.FIELD_SAVED)

    @pytest.mark.parametrize(
        'field_name,initial_context',
        [
            (QualFieldName.BUSINESS_ISSUES, 'Initial business issues context'),
            (QualFieldName.SCOPE_APPROACH, 'Initial scope and approach context'),
            (QualFieldName.VALUE_DELIVERED, 'Initial value delivered context'),
            (QualFieldName.ENGAGEMENT_SUMMARY, 'Initial engagement summary context'),
            (QualFieldName.ONE_LINE_DESCRIPTION, 'Initial one line description'),
        ],
    )
    async def test_command_processing_different_field_values(
        self,
        field_name: QualFieldName,
        initial_context: str,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test command processing works for all field values."""
        # Prepare command data for specific field
        command_data = {
            'command': TextEditCommand.EXPAND.value,
            'field_name': field_name.value,
            'context': initial_context,
            'formatted_context': initial_context,
            'snippet': 'Initial',
        }

        # The TextEditService should return only the edited snippet, not the full context
        expected_edited_snippet = 'EXPANDED Initial'
        expected_reconstructed_context = initial_context.replace('Initial', expected_edited_snippet)

        with patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit:
            mock_text_edit.return_value = expected_edited_snippet

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            form_data = {
                'conversation_id': str(test_conversation_id),
                'content': '',
                'command': json.dumps(command_data),
                'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
            }

            response = await async_client.post(message_url, headers=auth_header, data=form_data)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()

        # Verify the command was processed for the correct field
        # The command is in the user message, and the processed result is in the system message fields
        returned_command = data['user']['command']
        assert returned_command['field_name'] == field_name.value

        # The processed result is in the system message qual_fields
        system_fields = data['system']['qual_fields']

        # The processed result is in the system message fields
        # No field name mapping needed - the field names in the system message match the enum values
        processed_field = system_fields[field_name.value]
        assert processed_field['context'] == expected_reconstructed_context

    async def test_command_with_existing_extracted_data(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
        extracted_data_repository_real_with_autocommit,
    ):
        """Test command processing when there's existing extracted data for the field."""
        # Setup: Create existing extracted data
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.PROMPT
        )
        extracted_data.business_issues = 'Existing business issues from previous extraction'
        await extracted_data_repository_real_with_autocommit.update(extracted_data)

        # Prepare command data that will add field value data
        command_data = {
            'command': TextEditCommand.REWRITE.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': 'New business issues context for command processing',
            'formatted_context': 'New business issues context for command processing',
            'snippet': 'business issues context',
        }

        # The TextEditService should return only the edited snippet, not the full context
        expected_edited_snippet = 'REWRITTEN business issues context with new perspective'

        with patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit:
            mock_text_edit.return_value = expected_edited_snippet

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            form_data = {
                'conversation_id': str(test_conversation_id),
                'content': '',
                'command': json.dumps(command_data),
                'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
            }

            response = await async_client.post(message_url, headers=auth_header, data=form_data)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()

        # Verify command was processed successfully
        returned_command = data['user']['command']
        assert returned_command['command'] == TextEditCommand.REWRITE.value
        assert returned_command['context'] == 'New business issues context for command processing'  # Original context

        # Verify existing extracted data still exists (command should not modify it)
        updated_extracted_data = await extracted_data_repository_real_with_autocommit.get(
            test_conversation_id, DataSourceType.PROMPT
        )
        assert updated_extracted_data is not None
        assert updated_extracted_data.business_issues == 'Existing business issues from previous extraction'

    async def test_multiple_commands_sequence(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test processing multiple commands in sequence."""
        message_url = url_resolver.reverse(operation_ids.message.CREATE)

        # First command: EXPAND
        command_data_1 = {
            'command': TextEditCommand.EXPAND.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': 'Initial business issues text',
            'formatted_context': 'Initial business issues text',
            'snippet': 'business issues',
        }

        with patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit_1:
            mock_text_edit_1.return_value = 'EXPANDED initial business issues text with more detail'

            form_data_1 = {
                'conversation_id': str(test_conversation_id),
                'content': '',
                'command': json.dumps(command_data_1),
                'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
            }

            response_1 = await async_client.post(message_url, headers=auth_header, data=form_data_1)

        assert response_1.status_code == status.HTTP_201_CREATED

        # Second command: REWRITE
        command_data_2 = {
            'command': TextEditCommand.REWRITE.value,
            'field_name': QualFieldName.SCOPE_APPROACH.value,
            'context': 'Initial scope and approach description',
            'formatted_context': 'Initial scope and approach description',
            'snippet': 'scope and approach',
        }

        with patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit_2:
            mock_text_edit_2.return_value = 'REWRITTEN scope and approach description with new perspective'

            form_data_2 = {
                'conversation_id': str(test_conversation_id),
                'content': '',
                'command': json.dumps(command_data_2),
                'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
            }

            response_2 = await async_client.post(message_url, headers=auth_header, data=form_data_2)

        assert response_2.status_code == status.HTTP_201_CREATED

        # Verify both commands were processed independently
        data_1 = response_1.json()
        data_2 = response_2.json()

        assert data_1['user']['command']['field_name'] == QualFieldName.BUSINESS_ISSUES.value
        assert data_2['user']['command']['field_name'] == QualFieldName.SCOPE_APPROACH.value

    async def test_command_error_handling_text_edit_failure(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test command processing handles TextEditService failures gracefully."""
        command_data = {
            'command': TextEditCommand.EXPAND.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': 'Context for testing error handling',
            'formatted_context': 'Context for testing error handling',
            'snippet': 'testing error',
        }

        # Mock TextEditService to raise an exception
        with patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit:
            mock_text_edit.side_effect = Exception('OpenAI service unavailable')

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            form_data = {
                'conversation_id': str(test_conversation_id),
                'content': '',
                'command': json.dumps(command_data),
                'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
            }

            response = await async_client.post(message_url, headers=auth_header, data=form_data)

        # The API should handle the error gracefully
        # The exact behavior depends on error handling implementation
        # It might return 500 or a specific error response
        assert response.status_code in [status.HTTP_500_INTERNAL_SERVER_ERROR, status.HTTP_400_BAD_REQUEST]

    async def test_command_with_non_field_value_page_type(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test that commands are not processed for non-field-value page types."""
        command_data = {
            'command': TextEditCommand.EXPAND.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': 'Context for testing page type restriction',
            'formatted_context': 'Context for testing page type restriction',
            'snippet': 'testing',
        }

        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        form_data = {
            'conversation_id': str(test_conversation_id),
            'content': 'Regular message content',
            'command': json.dumps(command_data),
            'page_type': PageType.PROMPT.value,  # Not engagement_description
        }

        response = await async_client.post(message_url, headers=auth_header, data=form_data)

        # Should succeed but command should not be processed
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()

        # For PROMPT page type, the system message should not contain command processing
        # The command should be in the user message but not processed in the system message
        assert 'command' not in data['system']
        # The system message should still be of type TEXT, but without command processing


class TestFieldValueRepositoryCommandIntegration:
    """Tests for FieldValueRepository integration with commands."""

    @pytest.fixture
    def field_value_repository_mock(self):
        """Create a mock field value repository."""
        return AsyncMock()

    async def test_repository_create_called_with_correct_parameters(
        self,
        field_value_repository_mock,
    ):
        """Test that repository create is called with correct parameters."""
        from services.command import CommandService

        # Setup
        text_edit_service = AsyncMock()
        text_edit_service.text_edit.return_value = 'Edited text result'
        openai_repository = AsyncMock()
        conversation_repository = AsyncMock()

        command_service = CommandService(
            field_repository=field_value_repository_mock,
            text_edit_service=text_edit_service,
            openai_repository=openai_repository,
            conversation_repository=conversation_repository,
            extracted_data_service=AsyncMock(),
        )

        command = Command(
            command=TextEditCommand.EXPAND,
            field_name=QualFieldName.BUSINESS_ISSUES,
            context='Original context text',
            formatted_context='Original context text',
            snippet='context text',
        )

        conversation_id = uuid4()

        # Execute
        await command_service.process_command(command, 'User content', conversation_id)

        # Verify repository was called for original and regenerated fields
        field_value_repository_mock.create.assert_any_call(
            conversation_id=conversation_id,
            field_name=QualFieldName.BUSINESS_ISSUES,
            field_value='Original context text',
            formatted_field_value='Original context text',
        )
        field_value_repository_mock.create.assert_any_call(
            conversation_id=conversation_id,
            field_name=QualFieldName.ENGAGEMENT_SUMMARY,
            field_value=field_value_repository_mock.create.call_args_list[1][1]['field_value'],
            formatted_field_value=field_value_repository_mock.create.call_args_list[1][1]['formatted_field_value'],
        )
        field_value_repository_mock.create.assert_any_call(
            conversation_id=conversation_id,
            field_name=QualFieldName.ONE_LINE_DESCRIPTION,
            field_value=field_value_repository_mock.create.call_args_list[2][1]['field_value'],
            formatted_field_value=field_value_repository_mock.create.call_args_list[2][1]['formatted_field_value'],
        )
        # Optionally, assert the number of calls
        assert field_value_repository_mock.create.call_count == 3

    async def test_repository_stores_original_context_not_edited(
        self,
        field_value_repository_mock,
    ):
        """Test that repository stores the original context, not the edited version."""
        from services.command import CommandService

        # Setup
        text_edit_service = AsyncMock()
        text_edit_service.text_edit.return_value = 'COMPLETELY DIFFERENT edited text'
        openai_repository = AsyncMock()
        conversation_repository = AsyncMock()

        command_service = CommandService(
            field_repository=field_value_repository_mock,
            text_edit_service=text_edit_service,
            openai_repository=openai_repository,
            conversation_repository=conversation_repository,
            extracted_data_service=AsyncMock(),
        )

        original_context = 'This is the original context that should be stored'
        command = Command(
            command=TextEditCommand.REWRITE,
            field_name=QualFieldName.SCOPE_APPROACH,
            context=original_context,
            formatted_context=original_context,
            snippet='original context',
        )

        conversation_id = uuid4()

        # Execute
        result = await command_service.process_command(command, 'User content', conversation_id)

        # Verify repository was called for original and regenerated fields
        field_value_repository_mock.create.assert_any_call(
            conversation_id=conversation_id,
            field_name=QualFieldName.SCOPE_APPROACH,
            field_value=original_context,  # Original, not edited
            formatted_field_value=original_context,  # Original formatted context
        )
        field_value_repository_mock.create.assert_any_call(
            conversation_id=conversation_id,
            field_name=QualFieldName.ENGAGEMENT_SUMMARY,
            field_value=field_value_repository_mock.create.call_args_list[1][1]['field_value'],
            formatted_field_value=field_value_repository_mock.create.call_args_list[1][1]['formatted_field_value'],
        )
        field_value_repository_mock.create.assert_any_call(
            conversation_id=conversation_id,
            field_name=QualFieldName.ONE_LINE_DESCRIPTION,
            field_value=field_value_repository_mock.create.call_args_list[2][1]['field_value'],
            formatted_field_value=field_value_repository_mock.create.call_args_list[2][1]['formatted_field_value'],
        )
        # Optionally, assert the number of calls
        assert field_value_repository_mock.create.call_count == 3

        # But the result should contain the reconstructed context (before + edited + after)
        # The snippet 'original context' in 'This is the original context that should be stored'
        # text_before = 'This is the ', text_after = ' that should be stored'
        expected_reconstructed = 'This is the COMPLETELY DIFFERENT edited text that should be stored'
        if result.command:
            assert result.command.context == expected_reconstructed

    async def test_undo_command_reverts_field_value(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test that the UNDO command reverts a field to its previous value."""
        message_url = url_resolver.reverse(operation_ids.message.CREATE)

        # Step 1: Set an initial value for the business_issues field
        initial_context = 'This is the first version of the business issues.'
        command_data_1 = {
            'command': TextEditCommand.STORE.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': initial_context,
            'formatted_context': initial_context,
            'snippet': initial_context,
        }
        form_data_1 = {
            'conversation_id': str(test_conversation_id),
            'content': '',
            'command': json.dumps(command_data_1),
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }
        response_1 = await async_client.post(message_url, headers=auth_header, data=form_data_1)
        assert response_1.status_code == status.HTTP_201_CREATED

        # Step 2: Update the business_issues field with a new value
        updated_context = 'This is the second version of the business issues, which will be undone.'
        command_data_2 = {
            'command': TextEditCommand.STORE.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': updated_context,
            'formatted_context': updated_context,
            'snippet': updated_context,
        }
        form_data_2 = {
            'conversation_id': str(test_conversation_id),
            'content': '',
            'command': json.dumps(command_data_2),
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }
        response_2 = await async_client.post(message_url, headers=auth_header, data=form_data_2)
        assert response_2.status_code == status.HTTP_201_CREATED

        # Step 3: Use the UNDO command to revert the change
        undo_command_data = {
            'command': TextEditCommand.UNDO.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': updated_context,
            'formatted_context': updated_context,
            'snippet': '',
        }
        form_data_3 = {
            'conversation_id': str(test_conversation_id),
            'content': '',
            'command': json.dumps(undo_command_data),
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }
        response_3 = await async_client.post(message_url, headers=auth_header, data=form_data_3)
        assert response_3.status_code == status.HTTP_201_CREATED

        # Verify that the field has been reverted to the initial context
        data = response_3.json()
        system_fields = data['system']['qual_fields']
        assert system_fields[QualFieldName.BUSINESS_ISSUES.value]['context'] == ''

    async def test_summary_regeneration_on_field_update(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test that summaries are regenerated when a trigger field is updated."""
        message_url = url_resolver.reverse(operation_ids.message.CREATE)

        # Step 1: Set initial values for all regenerator fields
        initial_business_issues = 'Initial business issues.'
        initial_scope_approach = 'Initial scope and approach.'
        initial_value_delivered = 'Initial value delivered.'

        for context in [initial_business_issues, initial_scope_approach, initial_value_delivered]:
            command_data = {
                'command': TextEditCommand.STORE.value,
                'field_name': QualFieldName.BUSINESS_ISSUES.value,
                'context': context,
                'formatted_context': context,
                'snippet': context,
            }
            form_data = {
                'conversation_id': str(test_conversation_id),
                'content': '',
                'command': json.dumps(command_data),
                'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
            }
            await async_client.post(message_url, headers=auth_header, data=form_data)

        # Step 2: Update one of the regenerator fields
        updated_business_issues = 'Updated business issues that should trigger regeneration.'
        command_data_update = {
            'command': TextEditCommand.STORE.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': updated_business_issues,
            'formatted_context': updated_business_issues,
            'snippet': updated_business_issues,
        }
        form_data_update = {
            'conversation_id': str(test_conversation_id),
            'content': '',
            'command': json.dumps(command_data_update),
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        with patch('services.command.CommandService._regenerate_field') as mock_regenerate:
            mock_regenerate.side_effect = [
                'Regenerated Engagement Summary',
                'Regenerated One-Line Description',
            ]
            response = await async_client.post(message_url, headers=auth_header, data=form_data_update)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        system_fields = data['system']['qual_fields']

        # Verify that the summaries were regenerated
        assert system_fields[QualFieldName.ENGAGEMENT_SUMMARY.value]['context'] == 'Regenerated Engagement Summary'
        assert system_fields[QualFieldName.ONE_LINE_DESCRIPTION.value]['context'] == 'Regenerated One-Line Description'
        assert mock_regenerate.call_count == 2

    async def test_end_to_end_undo_flow(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test the complete end-to-end undo flow."""
        message_url = url_resolver.reverse(operation_ids.message.CREATE)

        # Step 1: Set an initial value for the business_issues field
        initial_context = 'This is the first version of the business issues.'
        command_data_1 = {
            'command': TextEditCommand.STORE.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': initial_context,
            'formatted_context': initial_context,
            'snippet': initial_context,
        }
        form_data_1 = {
            'conversation_id': str(test_conversation_id),
            'content': '',
            'command': json.dumps(command_data_1),
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }
        response_1 = await async_client.post(message_url, headers=auth_header, data=form_data_1)
        assert response_1.status_code == status.HTTP_201_CREATED

        # Step 2: Update the business_issues field with a new value
        updated_context = 'This is the second version of the business issues, which will be undone.'
        command_data_2 = {
            'command': TextEditCommand.EXPAND.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': updated_context,
            'formatted_context': updated_context,
            'snippet': updated_context,
        }
        form_data_2 = {
            'conversation_id': str(test_conversation_id),
            'content': '',
            'command': json.dumps(command_data_2),
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }
        with patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit:
            mock_text_edit.return_value = updated_context
            response_2 = await async_client.post(message_url, headers=auth_header, data=form_data_2)
        assert response_2.status_code == status.HTTP_201_CREATED
        data_2 = response_2.json()
        assert data_2['system']['is_undoable'] is True
        message_2_id = data_2['system']['id']

        # Step 3: Issue a new message, which should disable the undoable flag on the previous message
        message_data_3 = {
            'conversation_id': str(test_conversation_id),
            'content': 'Some other message',
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }
        response_3 = await async_client.post(message_url, headers=auth_header, data=message_data_3)
        assert response_3.status_code == status.HTTP_201_CREATED

        # Verify the undoable flag on the second message is now false
        message_get_url = url_resolver.reverse(operation_ids.message.GET, message_id=message_2_id)
        message_get_response = await async_client.get(message_get_url, headers=auth_header)
        assert message_get_response.status_code == status.HTTP_200_OK
        assert message_get_response.json()['is_undoable'] is False

        # Step 4: Use the UNDO command to revert the change
        undo_command_data = {
            'command': TextEditCommand.UNDO.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': updated_context,
            'formatted_context': updated_context,
            'snippet': '',
        }
        form_data_4 = {
            'conversation_id': str(test_conversation_id),
            'content': '',
            'command': json.dumps(undo_command_data),
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }
        response_4 = await async_client.post(message_url, headers=auth_header, data=form_data_4)
        assert response_4.status_code == status.HTTP_201_CREATED

        # Verify that the field has been reverted to the initial context
        data = response_4.json()
        system_fields = data['system']['qual_fields']
        assert system_fields[QualFieldName.BUSINESS_ISSUES.value]['context'] == ''

    async def test_store_command_saves_context_without_modification(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test that the STORE command saves the context without modification."""
        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        context_to_store = 'This is the exact content that should be stored.'
        command_data = {
            'command': TextEditCommand.STORE.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': context_to_store,
            'formatted_context': context_to_store,
            'snippet': '',
        }
        form_data = {
            'conversation_id': str(test_conversation_id),
            'content': '',
            'command': json.dumps(command_data),
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        with patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit:
            response = await async_client.post(message_url, headers=auth_header, data=form_data)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        system_fields = data['system']['qual_fields']

        # Verify that the context was stored exactly as provided
        assert system_fields[QualFieldName.BUSINESS_ISSUES.value]['context'] == context_to_store
        # Verify that the text_edit service was not called for the STORE command
        mock_text_edit.assert_not_called()
