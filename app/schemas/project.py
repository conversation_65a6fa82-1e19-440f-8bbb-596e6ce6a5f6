from typing import Any

from pydantic import BaseModel


__all__ = ['ClientIndustryData', 'ClientServiceData', 'SourceOfWorkData']


class ClientIndustryData(BaseModel):
    id: int
    isPrimary: bool
    aliases: list[str]
    fullPath: str
    locationId: int
    externalId: int
    globalId: int
    externalGlobalId: int
    parents: list[dict[str, Any]]
    riskAliases: list[str]
    name: str


class ClientServiceData(BaseModel):
    id: int
    isPrimary: bool
    aliases: list[str]
    fullPath: str
    locationId: int
    externalId: int
    globalId: int
    externalGlobalId: int
    parents: list[dict[str, Any]]
    riskAliases: list[str]
    name: str


class SourceOfWorkData(BaseModel):
    id: int
    name: str
