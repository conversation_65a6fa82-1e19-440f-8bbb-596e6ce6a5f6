"""Enhanced processing orchestrator for Azure Durable Functions.

This module contains the main orchestrator and sub-orchestrator for enhanced
data processing, implementing a fan-out/fan-in pattern with progress tracking.
"""

import logging
import re
from typing import Any

import azure.durable_functions as df
from azure.durable_functions import DurableOrchestrationContext

from constants.durable_functions import ActivityName, EntityOperation, EventType, OrchestratorName
from constants.extracted_data import DataSourceType, EnhancedExtractionField, Tense
from constants.prompt import prompt_templates
from durable_functions.activities.models import (
    EnhancedExtractionActivityInput,
    EnhancedExtractionActivityOutput,
    FieldExtractionTask,
    ListBlobsActivityInput,
    ListBlobsActivityOutput,
    SaveExtractionDataActivityInput,
    SendNotificationActivityInput,
)

from .models import EnhancedProcessingInput


logger = logging.getLogger(__name__)
bp = df.Blueprint()

# Constants for better maintainability
PROCESSING_STEPS_PER_BLOB = 3  # summarize, extract, save
DEFAULT_SYSTEM_PROMPT_TENSES = {
    EnhancedExtractionField.ENGAGEMENT_TITLE: Tense.PRESENT,
    EnhancedExtractionField.BUSINESS_ISSUES: Tense.PRESENT,
    EnhancedExtractionField.SCOPE_APPROACH: Tense.PRESENT,
    EnhancedExtractionField.VALUE_DELIVERED: Tense.PAST,
    EnhancedExtractionField.ENGAGEMENT_SUMMARY: Tense.PRESENT,
    EnhancedExtractionField.ONE_LINE_DESCRIPTION: Tense.PRESENT,
}


def _sanitize_roles_for_prompt(roles: list[str]) -> str:
    """Sanitize roles to prevent prompt injection."""
    sanitized_roles = []
    for role in roles:
        # Remove potentially dangerous characters and limit length
        sanitized_role = re.sub(r'[{}\\"\'\n\r\t]', '', role)[:100]
        if sanitized_role.strip():
            sanitized_roles.append(sanitized_role.strip())
    return '\n'.join(sanitized_roles)


def _create_primary_extraction_tasks(
    other_field_values: str,
    confirmed_context: str,
    project_roles: list[str],
    tense: Tense | str | None,
    client_industries: str,
    client_services: str,
) -> list[FieldExtractionTask]:
    """Create a list of field extraction tasks based on concatenated other content."""
    processed_roles = _sanitize_roles_for_prompt(project_roles)
    engagement_title_tense: Tense = Tense(
        tense if tense else DEFAULT_SYSTEM_PROMPT_TENSES[EnhancedExtractionField.ENGAGEMENT_TITLE]
    )
    business_issues_tense: Tense = Tense(
        tense if tense else DEFAULT_SYSTEM_PROMPT_TENSES[EnhancedExtractionField.BUSINESS_ISSUES]
    )
    scope_approach_tense = Tense(
        tense if tense else DEFAULT_SYSTEM_PROMPT_TENSES[EnhancedExtractionField.SCOPE_APPROACH]
    )
    value_delivered_tense = Tense(
        tense if tense else DEFAULT_SYSTEM_PROMPT_TENSES[EnhancedExtractionField.VALUE_DELIVERED]
    )

    return [
        FieldExtractionTask(
            field_name=EnhancedExtractionField.CLIENT_REFERENCES,
            context=other_field_values,
            system_prompt=prompt_templates.extract_client_references.SYSTEM,
            user_prompt=prompt_templates.extract_client_references.USER.format(input=other_field_values),
        ),
        FieldExtractionTask(
            field_name=EnhancedExtractionField.CLIENT_NAME_SHARING,
            context=other_field_values,
            system_prompt=prompt_templates.extract_client_name_sharing.SYSTEM,
            user_prompt=prompt_templates.extract_client_name_sharing.USER.format(input=other_field_values),
        ),
        FieldExtractionTask(
            field_name=EnhancedExtractionField.QUAL_USAGE,
            context=other_field_values,
            system_prompt=prompt_templates.extract_qual_usage.SYSTEM,
            user_prompt=prompt_templates.extract_qual_usage.USER.format(input=other_field_values),
        ),
        FieldExtractionTask(
            field_name=EnhancedExtractionField.TEAM_ROLES,
            context=other_field_values,
            system_prompt=prompt_templates.extract_project_roles.SYSTEM.replace('LIST_OF_ROLES', processed_roles),
            user_prompt=prompt_templates.extract_project_roles.USER.format(input=other_field_values),
        ),
        FieldExtractionTask(
            field_name=EnhancedExtractionField.ENGAGEMENT_FEE,
            context=other_field_values,
            system_prompt=prompt_templates.engagement_fee.SYSTEM,
            user_prompt=prompt_templates.engagement_fee.USER.format(input=other_field_values),
        ),
        FieldExtractionTask(
            field_name=EnhancedExtractionField.ENGAGEMENT_TITLE,
            context=other_field_values,
            system_prompt=prompt_templates.extract_engagement_title.SYSTEM.format(tense=str(engagement_title_tense)),
            user_prompt=prompt_templates.extract_engagement_title.USER.format(input=other_field_values),
        ),
        FieldExtractionTask(
            field_name=EnhancedExtractionField.BUSINESS_ISSUES,
            context=confirmed_context,
            system_prompt=prompt_templates.extract_business_issues.SYSTEM.format(tense=str(business_issues_tense)),
            user_prompt=prompt_templates.extract_business_issues.USER.format(input=confirmed_context),
        ),
        FieldExtractionTask(
            field_name=EnhancedExtractionField.SCOPE_APPROACH,
            context=confirmed_context,
            system_prompt=prompt_templates.extract_scope_and_approach.SYSTEM.format(tense=scope_approach_tense),
            user_prompt=prompt_templates.extract_scope_and_approach.USER.format(input=confirmed_context),
        ),
        FieldExtractionTask(
            field_name=EnhancedExtractionField.VALUE_DELIVERED,
            context=confirmed_context,
            system_prompt=prompt_templates.extract_value_and_impact.SYSTEM.format(tense=str(value_delivered_tense)),
            user_prompt=prompt_templates.extract_value_and_impact.USER.format(input=confirmed_context),
        ),
        FieldExtractionTask(
            field_name=EnhancedExtractionField.CLIENT_INDUSTRIES,
            context=other_field_values,
            system_prompt=prompt_templates.extract_client_industries.SYSTEM.format(client_industries=client_industries),
            user_prompt=prompt_templates.extract_client_industries.USER.format(input=other_field_values),
        ),
        FieldExtractionTask(
            field_name=EnhancedExtractionField.SOURCE_OF_WORK,
            context=other_field_values,
            system_prompt=prompt_templates.extract_source_of_work.SYSTEM,
            user_prompt=prompt_templates.extract_source_of_work.USER.format(input=other_field_values),
        ),
        FieldExtractionTask(
            field_name=EnhancedExtractionField.CLIENT_SERVICES,
            context=other_field_values,
            system_prompt=prompt_templates.extract_client_services.SYSTEM.format(client_services=client_services),
            user_prompt=prompt_templates.extract_client_services.USER.format(input=other_field_values),
        ),
        FieldExtractionTask(
            field_name=EnhancedExtractionField.ENGAGEMENT_LOCATIONS,
            context=other_field_values,
            system_prompt=prompt_templates.extract_engagement_locations.SYSTEM,
            user_prompt=prompt_templates.extract_engagement_locations.USER.format(input=other_field_values),
        ),
    ]


def _create_secondary_extraction_tasks(
    secondary_context: str,
    tense: Tense | str | None,
) -> list[FieldExtractionTask]:
    """Create a list of field extraction tasks based on concatenated other content."""
    engagement_summary_tense: Tense = Tense(
        tense if tense else DEFAULT_SYSTEM_PROMPT_TENSES[EnhancedExtractionField.ENGAGEMENT_SUMMARY]
    )
    one_line_summary_tense = Tense(
        tense if tense else DEFAULT_SYSTEM_PROMPT_TENSES[EnhancedExtractionField.ONE_LINE_DESCRIPTION]
    )
    return [
        FieldExtractionTask(
            field_name=EnhancedExtractionField.ENGAGEMENT_SUMMARY,
            context=secondary_context,
            system_prompt=prompt_templates.extract_engagement_summary.SYSTEM.format(
                tense=str(engagement_summary_tense)
            ),
            user_prompt=prompt_templates.extract_engagement_summary.USER.format(input=secondary_context),
        ),
        FieldExtractionTask(
            field_name=EnhancedExtractionField.ONE_LINE_DESCRIPTION,
            context=secondary_context,
            system_prompt=prompt_templates.extract_one_line_summary.SYSTEM.format(tense=str(one_line_summary_tense)),
            user_prompt=prompt_templates.extract_one_line_summary.USER.format(input=secondary_context),
        ),
    ]


def _create_progress_notification_input(
    signalr_user_id: str, message_id: str, progress_percent: int
) -> SendNotificationActivityInput:
    """Create a progress notification input for SignalR."""
    return SendNotificationActivityInput(
        event_type=EventType.DraftQualProgress,
        data={'percent': progress_percent, 'message_id': message_id},
        signalr_user_id=signalr_user_id,
    )


def _send_progress_update(context: DurableOrchestrationContext, entity_id: df.EntityId, signalr_user_id: str | None):
    """Helper function to increment progress and send notifications.

    Args:
        context: The durable orchestration context.
        entity_id: The entity ID for the progress tracker.
        signalr_user_id: The SignalR user ID for notifications.
    """
    if signalr_user_id:
        progress_data: dict[str, Any] = yield context.call_entity(entity_id, EntityOperation.INCREMENT)
        if progress_data:
            percent = progress_data.get('percent', 0)
            all_message_ids = progress_data.get('message_ids', [])
            # Send notification for only the first message ID to avoid duplicates
            if all_message_ids:
                yield context.call_activity(
                    ActivityName.SendNotification,
                    _create_progress_notification_input(signalr_user_id, all_message_ids[0], percent),
                )


@bp.orchestration_trigger('context', OrchestratorName.EnchancedProcessingSubOrchestrator)
def process_message(context: DurableOrchestrationContext):
    """Orchestrate the enhanced processing for a single message ID.

    This function processes a single message by listing its associated blobs,
    processing each one, and signaling a central entity to update the overall progress.

    Args:
        context: The durable orchestration context containing input data.
    """
    try:
        input_dict = context.get_input()
        if not input_dict:
            logger.error('No input provided for enhanced processing sub-orchestrator')
            return

        try:
            message_id = input_dict['message_id']
            signalr_user_id = input_dict.get('signalr_user_id')
            tense = input_dict['tense']
            entity_id_name = input_dict['entity_id_name']
            entity_id_key = input_dict['entity_id_key']
            entity_id = df.EntityId(entity_id_name, entity_id_key)
            confirmed_objective_and_scope = input_dict['confirmed_objective_and_scope']
            confirmed_outcomes = input_dict['confirmed_outcomes']
        except KeyError as e:
            logger.error(f'Missing required input field: {e}')
            return

        prefix = f'chunks_extracted/{message_id}/'.lower()
        blobs_outputs: list[ListBlobsActivityOutput] = yield context.call_activity(
            ActivityName.ListBlobs, ListBlobsActivityInput(prefix=prefix)
        )

        # Pre-load data needed for all blobs in this message
        project_roles_task = context.call_activity(ActivityName.LoadProjectRoles, {})
        # NOTE: Assumes new activities 'LoadClientIndustries' and 'LoadClientServices' will be created.
        client_industries_task = context.call_activity(ActivityName.LoadClientIndustries, {})
        client_services_task = context.call_activity(ActivityName.LoadClientServices, {})

        project_roles, client_industries_data, client_services_data = yield context.task_all(
            [project_roles_task, client_industries_task, client_services_task]
        )
        all_industries = [
            industry for country_data in client_industries_data.values() for industry in country_data.keys()
        ]
        unique_industries_str = '\n'.join([f'* {industry}' for industry in set(all_industries)])
        all_services_str = '\n'.join(f'* {service}' for service in client_services_data.keys())

        for blob_output in blobs_outputs:
            if not blob_output.chunks:
                continue

            # Step 1: Concat other chunks
            concat_other_fields = ' | '.join([chunk.other for chunk in blob_output.chunks if chunk.other])

            # Progress update after Concat
            yield from _send_progress_update(context, entity_id, signalr_user_id)

            if concat_other_fields:
                # Step 2a: Load project roles from blob storage
                project_roles: list[str] = yield context.call_activity(ActivityName.LoadProjectRoles, {})

                # Step 2b: Enhanced extraction with project roles
                confirmed_context_fields = [
                    confirmed_objective_and_scope,
                    confirmed_outcomes,
                ]
                if all(field and field.strip() for field in confirmed_context_fields):
                    confirmed_context = '\n'.join(confirmed_context_fields)
                else:
                    confirmed_context = concat_other_fields
                    logger.warning(
                        'No secondary context fields returned from enhanced extraction for message_id %s, fallback to other fields',
                        message_id,
                    )

                primary_tasks = _create_primary_extraction_tasks(
                    concat_other_fields,
                    confirmed_context,
                    project_roles,
                    tense,
                    unique_industries_str,
                    all_services_str,
                )
                enhanced_extraction_input = EnhancedExtractionActivityInput(
                    conversation_id=message_id, tasks=primary_tasks, source=blob_output.source
                )
                primary_extraction_result: EnhancedExtractionActivityOutput = yield context.call_activity(
                    ActivityName.EnhancedExtraction, enhanced_extraction_input
                )

                if primary_extraction_result and primary_extraction_result.extracted_data:
                    business_issues = primary_extraction_result.extracted_data.business_issues
                    scope_approach = primary_extraction_result.extracted_data.scope_approach
                    value_delivered = primary_extraction_result.extracted_data.value_delivered
                    # if these fields are empty, context for last 2 fields will be taken from others

                    secondary_context_fields = [
                        business_issues,
                        scope_approach,
                        value_delivered,
                    ]
                    if all(field and field.strip() for field in secondary_context_fields):
                        secondary_context = '\n'.join(secondary_context_fields)  # type: ignore[reportArgumentType]
                    else:
                        secondary_context = concat_other_fields
                        logger.warning(
                            'No secondary context fields returned from enhanced extraction for message %s, fallback to other fields',
                            message_id,
                        )

                    secondary_tasks = _create_secondary_extraction_tasks(secondary_context, tense)

                    # Step 2c: Enhanced extraction with secondary tasks
                    secondary_extraction_input = EnhancedExtractionActivityInput(
                        conversation_id=message_id, tasks=secondary_tasks, source=blob_output.source
                    )
                    secondary_extraction_result: EnhancedExtractionActivityOutput = yield context.call_activity(
                        ActivityName.EnhancedExtraction, secondary_extraction_input
                    )
                    if secondary_extraction_result and secondary_extraction_result.extracted_data:
                        primary_extraction_result.extracted_data.one_line_description = (
                            secondary_extraction_result.extracted_data.one_line_description
                        )
                        primary_extraction_result.extracted_data.engagement_summary = (
                            secondary_extraction_result.extracted_data.engagement_summary
                        )
                    else:
                        logger.warning(
                            'No secondary extraction result returned from enhanced extraction for message_id %s',
                            message_id,
                        )
                else:
                    logger.warning(
                        'No primary extraction result returned from enhanced extraction for message_id %s', message_id
                    )

                enhanced_extraction_result = primary_extraction_result
                # Progress update after extraction
                yield from _send_progress_update(context, entity_id, signalr_user_id)

                if enhanced_extraction_result and enhanced_extraction_result.extracted_data:
                    # Step 3: Save extraction data
                    save_extraction_input = SaveExtractionDataActivityInput(
                        message_id=message_id,
                        extraction_data=enhanced_extraction_result.extracted_data,
                        data_source_type=DataSourceType(blob_output.source),
                    )
                    save_extraction_data_output = yield context.call_activity(
                        ActivityName.SaveExtractionData, save_extraction_input
                    )
                    # Progress update after saving
                    yield from _send_progress_update(context, entity_id, signalr_user_id)
                    if save_extraction_data_output.replaced_confirmed_fields:
                        logger.warning(
                            f'Replaced confirmed fields in '
                            f'enchanced_processing_orchestrator:{save_extraction_data_output.replaced_confirmed_fields}'
                        )
                else:
                    # If no extraction result, still increment progress for the save step
                    yield from _send_progress_update(context, entity_id, signalr_user_id)
            else:
                # If no summarized content, still increment progress for extraction and save steps
                yield from _send_progress_update(context, entity_id, signalr_user_id)  # extraction step
                yield from _send_progress_update(context, entity_id, signalr_user_id)  # save step
    except Exception as e:
        logger.exception(f'Error detected in EnchancedProcessingSubOrchestrator(process_message): {e}')
        raise


def _validate_orchestrator_input(input_dict: dict[str, Any] | None, orch_name: str) -> EnhancedProcessingInput | None:
    """Validate and parse the orchestrator input data.

    Args:
        input_dict: Raw input dictionary from the orchestration context.
        orch_name: Name of the orchestrator for logging purposes.

    Returns:
        Parsed input data or None if validation fails.
    """
    if not input_dict:
        logger.error(f'No input provided for {orch_name}')
        return None

    try:
        input_data = EnhancedProcessingInput.model_validate(input_dict)
    except Exception as e:
        logger.error(f'Invalid input data for {orch_name}: {e}')
        return None

    if not input_data.message_ids:
        logger.error(f'No message IDs provided for {orch_name}')
        return None

    return input_data


def _send_initial_progress_notification(context: DurableOrchestrationContext, signalr_user_id: str, message_id: str):
    """Send initial 0% progress notification.

    Args:
        context: The durable orchestration context.
        signalr_user_id: The SignalR user ID for notifications.
        message_id: The message ID to use for the notification.
    """
    yield context.call_activity(
        ActivityName.SendNotification,
        _create_progress_notification_input(signalr_user_id, message_id, 0),
    )


def _calculate_total_processing_steps(context: DurableOrchestrationContext, message_ids: list[str]):
    """Calculate the total number of blobs and processing steps.

    Args:
        context: The durable orchestration context.
        message_ids: List of message IDs to process.

    Returns:
        Tuple of (total_blobs, total_processing_steps).
    """
    list_blob_tasks = [
        context.call_activity(
            ActivityName.ListBlobs,
            ListBlobsActivityInput(prefix=f'chunks_extracted/{message_id}/'.lower()),
        )
        for message_id in message_ids
    ]
    all_blob_outputs: list[list[ListBlobsActivityOutput]] = yield context.task_all(list_blob_tasks)
    total_blobs = sum(len(blobs) for blobs in all_blob_outputs)
    total_processing_steps = total_blobs * PROCESSING_STEPS_PER_BLOB

    return total_blobs, total_processing_steps


def _initialize_progress_tracker(
    context: DurableOrchestrationContext, total_processing_steps: int, message_ids: list[str]
):
    """Initialize the progress tracker entity.

    Args:
        context: The durable orchestration context.
        total_processing_steps: Total number of processing steps.
        message_ids: List of message IDs being processed.

    Returns:
        The entity ID for the progress tracker.
    """
    entity_id = df.EntityId('progress_tracker_entity', context.instance_id)
    yield context.call_entity(
        entity_id,
        EntityOperation.INITIALIZE,
        {'total_items': total_processing_steps, 'message_ids': message_ids},
    )
    return entity_id


def _execute_parallel_processing(
    context: DurableOrchestrationContext,
    message_ids: list[str],
    signalr_user_id: str | None,
    entity_id: df.EntityId,
    tense: str | None,
    confirmed_objective_and_scope: str,
    confirmed_outcomes: str,
):
    """Execute parallel processing using sub-orchestrators.

    Args:
        context: The durable orchestration context.
        message_ids: List of message IDs to process.
        signalr_user_id: The SignalR user ID for notifications.
        entity_id: The entity ID for the progress tracker.
        tense: The tense to use for extraction.
        confirmed_objective_and_scope: The objective and scope text for main fields extraction.
        confirmed_outcomes: The outcomes text for main fields extraction.
    """
    processing_tasks = [
        context.call_sub_orchestrator(
            OrchestratorName.EnchancedProcessingSubOrchestrator,
            {
                'message_id': message_id,
                'signalr_user_id': signalr_user_id,
                'entity_id_name': entity_id.name,
                'entity_id_key': entity_id.key,
                'tense': tense,
                'confirmed_objective_and_scope': confirmed_objective_and_scope,
                'confirmed_outcomes': confirmed_outcomes,
            },
        )
        for message_id in message_ids
    ]
    yield context.task_all(processing_tasks)


def _send_final_progress_notification(context: DurableOrchestrationContext, signalr_user_id: str, message_id: str):
    """Send final 100% progress notification.

    Args:
        context: The durable orchestration context.
        signalr_user_id: The SignalR user ID for notifications.
        message_id: The message ID to use for the notification.
    """
    yield context.call_activity(
        ActivityName.SendNotification,
        _create_progress_notification_input(signalr_user_id, message_id, 100),
    )


@bp.orchestration_trigger(context_name='context', orchestration=OrchestratorName.EnchancedExtraction)
def enchanced_processing_orchestrator(context: DurableOrchestrationContext):
    """Main orchestrator for enhanced data processing.

    This orchestrator implements a fan-out/fan-in pattern to process multiple
    message IDs in parallel, using a Durable Entity to track overall progress.

    The orchestrator performs the following steps:
    1. Validates input data
    2. Sends initial progress notification (0%)
    3. Calculates total processing steps
    4. Initializes progress tracker entity
    5. Executes parallel processing via sub-orchestrators
    6. Cleans up progress tracker
    7. Sends final progress notification (100%)

    Args:
        context: The durable orchestration context containing input data.
    """
    orch_name = str(OrchestratorName.EnchancedExtraction)
    input_dict = context.get_input()

    # Validate input data
    input_data: EnhancedProcessingInput | None = _validate_orchestrator_input(input_dict, orch_name)
    if not input_data:
        return

    # Send initial progress notification
    if input_data.signalr_user_id:
        yield from _send_initial_progress_notification(context, input_data.signalr_user_id, input_data.message_ids[0])

    try:
        # Calculate total processing steps
        total_blobs, total_processing_steps = yield from _calculate_total_processing_steps(
            context, input_data.message_ids
        )

        if total_blobs == 0:
            logger.info('No blobs to process.')
        else:
            # Initialize progress tracker and execute processing
            entity_id = yield from _initialize_progress_tracker(context, total_processing_steps, input_data.message_ids)

            yield from _execute_parallel_processing(
                context,
                input_data.message_ids,
                input_data.signalr_user_id,
                entity_id,
                input_data.tense,
                input_data.confirmed_objective_and_scope,
                input_data.confirmed_outcomes,
            )

            # Clean up progress tracker
            context.signal_entity(entity_id, EntityOperation.RESET)

        # Send final progress notification
        if input_data.signalr_user_id:
            yield from _send_final_progress_notification(
                context, input_data.signalr_user_id, input_data.message_ids[-1]
            )

        logger.info(f'Successfully completed {orch_name} for all message IDs.')

    except Exception:
        logger.exception(f'Error in {orch_name} orchestrator')
