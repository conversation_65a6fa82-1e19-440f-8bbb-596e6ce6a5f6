"""Enhanced extraction activities for comprehensive qual field processing."""

import logging
from typing import Any, Dict

import azure.durable_functions as df
from openai import AsyncAzureOpenAI

from constants.durable_functions import ActivityName
from constants.extracted_data import EnhancedExtractionField
from durable_functions.application.config import settings
from durable_functions.repositories.openai import OpenAIRepository
from durable_functions.utils.activity_logger import activity_logging_decorator
from durable_functions.utils.blob_storage import BlobStorageHelper
from durable_functions.utils.models import EngagementFeeExpected, FinalExtractionDataResults

from .models import (
    BaseExtractionResult,
    EnhancedExtractionActivityInput,
    EnhancedExtractionActivityOutput,
    ListExtractionResult,
    ProjectRolesExtractionResult,
)


logger = logging.getLogger(__name__)

bp = df.Blueprint()

FIELD_RESPONSE_MODEL_MAPPING = {
    EnhancedExtractionField.TEAM_ROLES: ProjectRolesExtractionResult,
    EnhancedExtractionField.ENGAGEMENT_FEE: EngagementFeeExpected,
    EnhancedExtractionField.CLIENT_INDUSTRIES: ListExtractionResult,
    EnhancedExtractionField.CLIENT_SERVICES: ListExtractionResult,
    EnhancedExtractionField.ENGAGEMENT_LOCATIONS: ListExtractionResult,
    # Add future mappings here
}


async def _enhanced_extraction(
    extraction_input: EnhancedExtractionActivityInput, openai_repo: OpenAIRepository
) -> EnhancedExtractionActivityOutput:
    extracted_fields = {}
    for task in extraction_input.tasks:
        logger.info(f'Executing enhanced extraction for field: {task.field_name}')
        response_model = FIELD_RESPONSE_MODEL_MAPPING.get(task.field_name, BaseExtractionResult)

        structured_response = await openai_repo.extract_structured_data(
            system_prompt=task.system_prompt,
            user_prompt=task.user_prompt,
            response_model=response_model,
        )
        if structured_response and structured_response.value:
            extracted_fields[str(task.field_name)] = structured_response.value
        else:
            logger.warning(f'No value extracted for {task.field_name}. Response: {structured_response}')
    logger.info(f'Extracted fields: {extracted_fields}')
    logger.info(f'Enhanced extraction completed for conversation: {extraction_input.conversation_id}')
    return EnhancedExtractionActivityOutput(
        conversation_id=extraction_input.conversation_id,
        source=extraction_input.source,
        extracted_data=FinalExtractionDataResults.model_validate(extracted_fields),
    )


@bp.activity_trigger(input_name='extraction_input', activity=ActivityName.EnhancedExtraction)
@activity_logging_decorator
async def enhanced_extraction_activity(
    extraction_input: EnhancedExtractionActivityInput,
) -> EnhancedExtractionActivityOutput:
    try:
        async with AsyncAzureOpenAI(
            azure_endpoint=settings.openai.endpoint,
            api_key=settings.openai.key,
            api_version=settings.openai.api_version,
        ) as client:
            openai_repo = OpenAIRepository(client=client)
            return await _enhanced_extraction(extraction_input, openai_repo)
    except Exception:
        logger.exception('Error in enhanced_extraction_activity')
        raise


@bp.activity_trigger('input_data', ActivityName.LoadProjectRoles)
def load_project_roles_activity(input_data: dict) -> list[str]:
    """Activity to load project roles from blob storage."""
    try:
        project_roles_blob_repo = BlobStorageHelper(
            connection_string=settings.BLOB_STORAGE_SETTINGS.CONNECTION_STRING,
            container_name=settings.BLOB_STORAGE_SETTINGS.DEFAULT_CONTAINER_NAME,
        )
        project_roles: Dict[str, Any] = project_roles_blob_repo.download_json('project-roles.json')
        if not project_roles or 'project_roles' not in project_roles:
            return []
        return [role for role in project_roles['project_roles'] if isinstance(role, str)]
    except Exception:
        logger.exception('Error in load_project_roles_activity')
        return []


@bp.activity_trigger('input_data', ActivityName.LoadClientIndustries)
def load_client_industries_activity(input_data: dict) -> dict[str, Any]:
    """Activity to load client industries from blob storage."""
    try:
        client_industries_blob_repo = BlobStorageHelper(
            connection_string=settings.BLOB_STORAGE_SETTINGS.CONNECTION_STRING,
            container_name=settings.BLOB_STORAGE_SETTINGS.DEFAULT_CONTAINER_NAME,
        )
        client_industries: Dict[str, Any] = client_industries_blob_repo.download_json('client-industries.json')
        return client_industries
    except Exception:
        logger.exception('Error in load_client_industries_activity')
        return {}


@bp.activity_trigger('input_data', ActivityName.LoadClientServices)
def load_client_services_activity(input_data: dict) -> dict[str, Any]:
    """Activity to load client services from blob storage."""
    try:
        client_services_blob_repo = BlobStorageHelper(
            connection_string=settings.BLOB_STORAGE_SETTINGS.CONNECTION_STRING,
            container_name=settings.BLOB_STORAGE_SETTINGS.DEFAULT_CONTAINER_NAME,
        )
        client_services: Dict[str, Any] = client_services_blob_repo.download_json('client-services.json')
        return client_services
    except Exception:
        logger.exception('Error in load_client_services_activity')
        return {}
