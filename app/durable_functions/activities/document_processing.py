from datetime import datetime, timezone
import json
import logging
import os
import re
from typing import Any, Dict, cast
from uuid import UUID

from azure.core.exceptions import HttpResponseError
import azure.durable_functions as df
from openai import AsyncAzureOpenAI
from sqlalchemy.ext.asyncio import AsyncSession

from constants.durable_functions import ActivityName, ExctractStatus
from constants.engagement import EngagementFeeDisplay
from constants.extracted_data import DataSourceType
from constants.prompt import EXTRACT_DATA_SYSTEM_PROMPT
from durable_functions.application.config import settings
from durable_functions.application.db import async_session_local
from durable_functions.repositories import (
    ConversationMessageRepository,
    ConversationRepository,
    DocumentQueueRepository,
    ExtractedDataRepository,
    OpenAIRepository,
    ProcessingMessageRepository,
)
from durable_functions.utils import (
    BlobStorageHelper,
    DocumentIntelligenceHelper,
    ExtractedDataMerger,
    ModelsSettings,
    RecursiveChunkingStrategy,
    SignalRApiClient,
    activity_logging_decorator,
    calculate_tokens,
    get_chunks_size,
)
from durable_functions.utils.models import (
    EngagementPeriod,
    FinalExtractionDataResults,
    FinalExtractionDataTranslationInput,
    LLMExtractedDataResult,
)
from schemas import ExtractedData, IndustryData, NewConfirmedDataForUpdate, ProcessingStatusUpdatePayload, ServiceData
from validators.extracted_data import check_confirmed_fields_replacement

from .models import (
    AggregateMultiSourceDataActivityInput,
    ChunkDocumentActivityInput,
    ChunkDocumentActivityOutput,
    ExtractDataActivityInput,
    ExtractDocumentTextActivityInput,
    ExtractDocumentTextActivityOutput,
    ExtractDocumentTextActivityOutputFailed,
    ListBlobsActivityInput,
    ListBlobsActivityOutput,
    ReadPromptActivityInput,
    SaveAggregatedResultsToBlobActivityInput,
    SaveExtractionDataActivityInput,
    SaveExtractionDataActivityOutput,
    SaveExtractionResultsToBlobActivityInput,
    SendFinalQueueMessageActivityInput,
    SendNotificationActivityInput,
    SendQueueMessageActivityInput,
    UpdateProcessingStatusActivityInput,
)


logger = logging.getLogger(__name__)


def _merge_with_existing_data(
    new_data: FinalExtractionDataResults, existing_data: ExtractedData | None
) -> FinalExtractionDataResults:
    """
    Merge new extraction data with existing data using field-specific strategies.

    Field Merging Rules:
    1. Client Names (client_name): MERGE unique values into a list - never override existing values
    2. LDMF Country (ldmf_country): MERGE unique values into a list - never override existing values
    3. Date Ranges (start_date/end_date): COLLECT and merge date intervals as pairs - never override existing ranges
    4. Objectives and Scope (objective_and_scope): OVERRIDE behavior - latest value replaces previous
    5. Outcomes: OVERRIDE behavior - latest value replaces previous

    Args:
        new_data: New extraction results to merge
        existing_data: Existing data from database (if any)

    Returns:
        FinalExtractionDataResults with properly merged data
    """
    if not existing_data:
        # No existing data, return new data as-is
        return new_data

    # Convert existing data to FinalExtractionDataResults format for merging
    existing_client_names = []
    existing_ldmf_countries = []
    existing_periods = []
    existing_periods_original = []
    existing_client_industries = []
    existing_client_services = []
    existing_engagement_locations = None

    # Parse existing client names - they're already parsed by the schema validator
    if existing_data.client_name:
        if isinstance(existing_data.client_name, list):
            existing_client_names = existing_data.client_name
        elif isinstance(existing_data.client_name, str):
            try:
                existing_client_names = json.loads(existing_data.client_name)
            except (json.JSONDecodeError, TypeError):
                logger.warning(f'Failed to parse existing client names: {existing_data.client_name}')

    # Parse existing LDMF countries - they're already parsed by the schema validator
    if existing_data.ldmf_country:
        if isinstance(existing_data.ldmf_country, list):
            existing_ldmf_countries = existing_data.ldmf_country
        elif isinstance(existing_data.ldmf_country, str):
            try:
                existing_ldmf_countries = json.loads(existing_data.ldmf_country)
            except (json.JSONDecodeError, TypeError):
                logger.warning(f'Failed to parse existing LDMF countries: {existing_data.ldmf_country}')

    # Convert existing date fields to periods
    if existing_data.start_date or existing_data.end_date:
        existing_periods.append(
            EngagementPeriod(
                start_date=existing_data.start_date.strftime('%Y-%m-%d') if existing_data.start_date else None,
                end_date=existing_data.end_date.strftime('%Y-%m-%d') if existing_data.end_date else None,
            )
        )

    if existing_data.start_date_original or existing_data.end_date_original:
        existing_periods_original.append(
            EngagementPeriod(start_date=existing_data.start_date_original, end_date=existing_data.end_date_original)
        )

    if existing_data.client_industries:
        existing_client_industries = [industry.name for industry in existing_data.client_industries]

    if existing_data.client_services:
        existing_client_services = [service.name for service in existing_data.client_services]

    if existing_data.engagement_locations:
        existing_engagement_locations = existing_data.engagement_locations.split(', ')

    # Create existing data structure for merging
    existing_final_data = FinalExtractionDataResults(
        client_names=existing_client_names if existing_client_names else None,
        lead_member_countries=existing_ldmf_countries if existing_ldmf_countries else None,
        periods=existing_periods if existing_periods else None,
        periods_original=existing_periods_original if existing_periods_original else None,
        objective_and_scope=existing_data.objective_and_scope,
        outcomes=existing_data.outcomes,
        engagement_title=existing_data.title,
        business_issues=existing_data.business_issues,
        scope_approach=existing_data.scope_approach,
        value_delivered=existing_data.value_delivered,
        engagement_summary=existing_data.engagement_summary,
        one_line_description=existing_data.one_line_description,
        qual_usage=existing_data.qual_usage,
        client_industries=existing_client_industries or None,
        client_services=existing_client_services or None,
        other=existing_data.other,
        engagement_locations=existing_engagement_locations,
    )

    # Use ExtractedDataMerger to merge the data with proper field-specific strategies
    source_results = [
        (DataSourceType.DOCUMENTS, existing_final_data),  # Existing data
        (DataSourceType.DOCUMENTS, new_data),  # New data
    ]

    merged_result = ExtractedDataMerger.merge_multi_source_results(source_results)

    # Apply override behavior for objective_and_scope and outcomes (latest value wins)
    if new_data.objective_and_scope:
        merged_result.objective_and_scope = new_data.objective_and_scope
    if new_data.outcomes:
        merged_result.outcomes = new_data.outcomes

    return merged_result


bp = df.Blueprint()


@bp.activity_trigger('document', ActivityName.ExtractDocumentText)
@activity_logging_decorator
def extract_document_text(
    document: ExtractDocumentTextActivityInput,
) -> ExtractDocumentTextActivityOutput | ExtractDocumentTextActivityOutputFailed:
    """
    Activity function to extract text from a document using Document Intelligence.
    """
    try:
        file_name = document.file_name
        logger.info(f'Extracting text from document {file_name} for message {document.message_id}')

        blob_helper = BlobStorageHelper()
        document_bytes = blob_helper.get_blob_from_url(document.blob_url)

        try:
            doc_intelligence = DocumentIntelligenceHelper()
            extraction_result = doc_intelligence.extract_text_from_document(document_bytes)
        except HttpResponseError as e:
            error_message = f'Document extraction failed: {str(e)}'
            is_corrupted = False
            if 'The file is corrupted or format is unsupported.' in e.message:
                error_message = 'The file is corrupted or format is unsupported.'
                is_corrupted = True
                logger.error(f'Document Intelligence extraction failed: {error_message}')

            return ExtractDocumentTextActivityOutputFailed(
                message_id=document.message_id,
                file_name=file_name,
                error=error_message,
                status=ExctractStatus.Failed,
                file_is_corrupted=is_corrupted,
            )

        except Exception as e:
            logger.error(f'Document Intelligence extraction failed: {str(e)}')
            return ExtractDocumentTextActivityOutputFailed(
                message_id=document.message_id,
                file_name=file_name,
                error=f'Document extraction failed: {str(e)}',
                status=ExctractStatus.Failed,
            )

        base_name = os.path.splitext(file_name)[0]  # type: ignore
        extract_filename = f'{base_name}.json'
        extraction_path = f'extracted/{document.message_id}/{extract_filename}'
        extraction_url = blob_helper.upload_json(extraction_path, extraction_result)

        return ExtractDocumentTextActivityOutput(
            message_id=document.message_id,
            file_name=file_name,
            extraction_url=extraction_url,
            text_content=extraction_result['text'],
            metadata={
                **extraction_result['metadata'],
                'original_blob_url': document.blob_url,
            },
            status=ExctractStatus.Success,
        )

    except Exception:
        logger.exception('Error extracting document text')
        raise


@bp.activity_trigger('updates', ActivityName.UpdateProcessingStatus)
@activity_logging_decorator
async def update_processing_status(updates: UpdateProcessingStatusActivityInput) -> Dict[str, Any]:
    """
    Activity function to update the file processing status.

    This function demonstrates how to integrate with FastAPI repositories.
    Currently logs the status update, but can be enhanced to use database repositories.
    """
    try:
        logger.info(f'Updating processing status for message_id {updates.message_id}: {updates.status}')

        async with cast(AsyncSession, async_session_local()) as session:
            process_message_repo = ProcessingMessageRepository(session)

            # Update message processing status in database
            await process_message_repo.create(
                data=ProcessingStatusUpdatePayload(
                    message_id=UUID(updates.message_id),
                    status=updates.status,
                    message=updates.message,
                    metadata=updates.metadata,
                )
            )
            await session.commit()

        logger.info(f'Processing status update: {updates.status} - {updates.message}')
        if updates.metadata:
            logger.info(f'Metadata: {updates.metadata}')

        return {
            'status': 'success',
            'message_id': updates.message_id,
            'processing_status': updates.status,
            'message': updates.message,
            'metadata': updates.metadata,
        }

    except Exception:
        logger.exception('Error updating processing status')
        raise


@bp.activity_trigger('notification', ActivityName.SendNotification)
@activity_logging_decorator
async def send_notification(notification: SendNotificationActivityInput) -> str | None:
    """
    Activity function to send a notification via SignalR.

    Args:
        notification: Notification data including event type, data, and optional signalr_user_id

    Returns:
        None
    """
    try:
        logger.info(f'Sending notification event: {notification.event_type}')
        signalr_client = SignalRApiClient()

        # Pass the signalr_user_id to the send_notification method
        await signalr_client.send_notification(
            event_type=notification.event_type, data=notification.data, user_id=notification.signalr_user_id
        )
        return f'notification success for {signalr_client.endpoint}'

    except Exception:
        logger.exception('Error sending notification')
        raise


@bp.activity_trigger('extraction', ActivityName.ChunkDocument)
@activity_logging_decorator
def chunk_document(extraction: ChunkDocumentActivityInput) -> ChunkDocumentActivityOutput:
    """
    Activity function to chunk a document.
    """
    try:
        message_id = extraction.message_id
        file_name = extraction.file_name
        text_content = extraction.text_content
        metadata = extraction.metadata

        logger.info(f'Chunking document {file_name} for message {message_id}')
        llm_model = ModelsSettings().get_settings()
        # Use dynamic chunking strategy for intelligent chunk size calculation
        empty_system_prompt_tokens = calculate_tokens(model_name=llm_model.deployment, text=EXTRACT_DATA_SYSTEM_PROMPT)
        chunk_size, overlap_size, text_total, max_tokens = get_chunks_size(
            system_prompt=empty_system_prompt_tokens,
            token_limits=llm_model.limit,
            input_to_output=0.2,
            chunk_to_overlap=0.05,
            max_user_length_percent=90,
        )
        logger.info(
            f'Calculated chunking parameters: chunk size {chunk_size}, overlap size {overlap_size}, text total {text_total}, max tokens {max_tokens}'
        )
        chunking_strategy = RecursiveChunkingStrategy(
            chunk_size=chunk_size,
            chunk_overlap=overlap_size,
        )
        document_metadata = {
            'message_id': message_id,
            'file_name': file_name,
        }
        for key, value in metadata.items():
            document_metadata[key] = value

        chunks = chunking_strategy.chunk_document(text_content, document_metadata)

        blob_helper = BlobStorageHelper()
        chunk_urls = []

        for chunk in chunks:
            base_name = os.path.splitext(file_name)[0]  # type: ignore
            chunk_filename = f'{base_name}_chunk_{chunk["chunk_index"]}.json'
            chunk_path = f'chunks/{message_id}/{chunk_filename}'
            chunk_url = blob_helper.upload_json(chunk_path, chunk)
            chunk_urls.append({'chunk_index': chunk['chunk_index'], 'chunk_id': chunk['chunk_id'], 'url': chunk_url})

        return ChunkDocumentActivityOutput(
            message_id=message_id,
            file_name=file_name,
            chunk_count=len(chunks),
            chunk_urls=chunk_urls,
        )

    except Exception:
        logger.exception('Error chunking document')
        raise


@bp.activity_trigger('extracted', ActivityName.DetectDocumentLanguage)
@activity_logging_decorator
async def detect_language(extracted: ExtractDataActivityInput) -> str | None:
    """
    Activity function to detect language of the document's chunk.
    """
    blob_helper = BlobStorageHelper()

    async with AsyncAzureOpenAI(
        azure_endpoint=settings.openai.endpoint,
        api_key=settings.openai.key,
        api_version=settings.openai.api_version,
    ) as client:
        openai_repo = OpenAIRepository(client=client)
        logger.info('Detecting language of text')
        try:
            if extracted.chunk_url:
                chunk_url = extracted.chunk_url
                # Use get_blob_from_url which handles full URLs and extracts the path correctly
                chunk_data_bytes = blob_helper.get_blob_from_url(str(chunk_url))
                chunk_data = json.loads(chunk_data_bytes.decode('utf-8'))
                text = chunk_data.get('text')
            elif extracted.text_content:
                text = extracted.text_content
            else:
                raise ValueError('No chunk_url or text_content provided')

            if not text or not text.strip():
                logger.warning('Empty or whitespace-only text provided for language detection')
                return None

            llm_detected_language = await openai_repo.detect_language(text)
            return llm_detected_language

        except Exception as e:
            logger.exception(f'Error detecting language: {e}')
            raise


@bp.activity_trigger('extracted', ActivityName.TranslateExtractedData)
@activity_logging_decorator
async def translate_extraction_result(extracted: FinalExtractionDataTranslationInput) -> FinalExtractionDataResults:
    """
    Activity function to translate extracted data fields from source language to target language.
    """

    async with AsyncAzureOpenAI(
        azure_endpoint=settings.openai.endpoint,
        api_key=settings.openai.key,
        api_version=settings.openai.api_version,
    ) as client:
        openai_repo = OpenAIRepository(client=client)
        try:
            extracted_data_dict = extracted.model_dump()
            source_language = extracted_data_dict.pop('source_language', 'english')

            logger.info(f'Translating extracted data from {source_language}')

            for field, value in extracted_data_dict.items():
                if isinstance(value, str) and value.strip():
                    # Translate only non-empty strings
                    translated_value = await openai_repo.translate_text(source_language=source_language, text=value)
                    extracted_data_dict[field] = translated_value
                elif isinstance(value, list) and value:
                    # Translate list items if they are strings
                    translated_list = []
                    for item in value:
                        if isinstance(item, str) and item.strip():
                            translated_item = await openai_repo.translate_text(
                                source_language=source_language, text=item
                            )
                            translated_list.append(translated_item)
                        else:
                            translated_list.append(item)
                    extracted_data_dict[field] = translated_list
            return FinalExtractionDataResults.model_validate(extracted_data_dict)
        except Exception as e:
            logger.exception(f'Error translating extraction result: {e}')
            raise


@bp.activity_trigger('message', ActivityName.SendQueueMessage)
@activity_logging_decorator
async def send_queue_message(message: SendQueueMessageActivityInput) -> None:
    queue_name = settings.QUEUE_SETTINGS.CONTENT_PROCESSING_QUEUE_CHUNKED

    try:
        logger.info(f'Sending chunk processing message for message_id: {message.message_id}')

        queue_repo = DocumentQueueRepository(
            connection_string=settings.QUEUE_SETTINGS.CONNECTION_STRING,
            queue_name=queue_name,
        )

        # Create a smaller message payload that references the chunks
        # instead of including all their URLs, to avoid exceeding the queue message size limit.
        message_data = {
            'message_id': message.message_id,
            'file_name': message.file_name,
            'chunk_count': message.chunk_count,
            'signalr_user_id': message.signalr_user_id,
            'input_type': message.input_type.value,
        }

        # Use the public send_message to send a raw dictionary,
        # assuming the consumer of this queue can handle this format.
        await queue_repo.send_message(message_data)

    except Exception as e:
        logger.error(f'Error sending queue message: {str(e)}')
        raise


@bp.activity_trigger('prompt', ActivityName.ReadPrompt)
@activity_logging_decorator
async def read_prompt(prompt: ReadPromptActivityInput) -> str:
    try:
        prompt_url = prompt.prompt_url
        logger.info(f'Reading prompt from {prompt_url}')
        blob_helper = BlobStorageHelper()
        prompt_text = blob_helper.get_blob_from_url(prompt_url).decode('utf-8')
        return prompt_text
    except Exception:
        logger.exception('Error reading prompt')
        raise


@bp.activity_trigger('extraction', ActivityName.ExtractData)
@activity_logging_decorator
async def extract_data(extraction: ExtractDataActivityInput) -> LLMExtractedDataResult:
    """
    Activity function to extract data from a document.
    """
    blob_helper = BlobStorageHelper()

    async with AsyncAzureOpenAI(
        azure_endpoint=settings.openai.endpoint,
        api_key=settings.openai.key,
        api_version=settings.openai.api_version,
    ) as client:
        openai_repo = OpenAIRepository(client=client)

        try:
            if extraction.chunk_url:
                chunk_url = extraction.chunk_url
                # Use get_blob_from_url which handles full URLs and extracts the path correctly
                chunk_data_bytes = blob_helper.get_blob_from_url(str(chunk_url))
                chunk_data = json.loads(chunk_data_bytes.decode('utf-8'))
                text = chunk_data.get('text')
            elif extraction.text_content:
                text = extraction.text_content
            else:
                raise ValueError('No chunk_url or text_content provided')

            if text and (stripped_text := text.strip()):
                logger.info('Extracting data from text')
                result = await openai_repo.extract_data(stripped_text)

                if not result:
                    return LLMExtractedDataResult()

                if not result.ldmf_country:
                    return result

                logger.info(f'Extracting and validating LDMF countries: {result.ldmf_country}')
                countries_blob_repo = BlobStorageHelper(
                    connection_string=settings.BLOB_STORAGE_SETTINGS.CONNECTION_STRING,
                    container_name=settings.BLOB_STORAGE_SETTINGS.DEFAULT_CONTAINER_NAME,
                )
                countries: Dict[str, Any] = countries_blob_repo.download_json('countries.json')

                ldmf_countries_list = [country['name'] for country in countries['countries']]
                result.ldmf_country = await openai_repo.extract_ldmf_countries(result.ldmf_country, ldmf_countries_list)

                return result
            else:
                return LLMExtractedDataResult()
        except Exception:
            logger.exception('Error extracting data')
            raise


@bp.activity_trigger('merging', ActivityName.MergeExtractionData)
@activity_logging_decorator
async def merge_extraction_data(merging) -> FinalExtractionDataResults:
    try:
        return ExtractedDataMerger.merge_results(merging)
    except Exception:
        logger.exception('Error merging extraction data')
        raise


def _prepare_data_for_update(
    conversation_id: UUID,
    data_source_type: DataSourceType,
    merged_data: FinalExtractionDataResults,
    saving_extraction_data: FinalExtractionDataResults,
    previous_extracted_data: ExtractedData | None,
) -> ExtractedData:
    """
    Prepares the ExtractedData object for database update by handling data transformations.
    """
    # Handle more_than_two_dates flag
    if previous_extracted_data and not merged_data.more_than_two_dates:
        more_than_two_dates = previous_extracted_data.more_than_two_dates
    elif merged_data.periods and len(merged_data.periods) > 1:
        more_than_two_dates = True
    else:
        more_than_two_dates = merged_data.more_than_two_dates

    # Serialize list fields for database storage
    client_name = json.dumps(merged_data.client_names or [])
    ldmf_country = json.dumps(merged_data.lead_member_countries or [])
    team_roles = json.dumps(merged_data.team_roles or [])
    engagement_locations = ', '.join(merged_data.engagement_locations) if merged_data.engagement_locations else None

    # Handle date periods
    start_date, end_date = None, None
    start_date_original, end_date_original = None, None
    start_date_index, end_date_index = None, None

    if merged_data.periods:
        # Collect all start and end dates separately to handle single dates properly
        for index, period in enumerate(merged_data.periods):
            if not start_date or (period.start_date and period.start_date < start_date):
                start_date = period.start_date
                start_date_index = index
            if not end_date or (period.end_date and period.end_date > end_date):
                end_date = period.end_date
                end_date_index = index

        start_date = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date else None
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else None

    if (
        merged_data.periods_original
        and merged_data.periods
        and len(merged_data.periods_original) == len(merged_data.periods)
    ):
        if start_date_index is not None:
            start_date_original = merged_data.periods_original[start_date_index].start_date
        if end_date_index is not None:
            end_date_original = merged_data.periods_original[end_date_index].end_date

    # Initialize engagement fee fields
    engagement_fee_amount = None
    engagement_fee_currency = None
    is_engagement_fee_unknown = False
    is_pro_bono = False
    engagement_fee_display = None

    # Flags to track what kind of fee information was found
    found_any_fee_info = False
    found_specific_amount = False
    found_range_amount = False
    found_pro_bono_indicator = False
    found_confidential_display = False

    if saving_extraction_data.engagement_fee:
        found_any_fee_info = True
        for fee_result in saving_extraction_data.engagement_fee:
            # Check for "Pro bono" indication '0'
            if fee_result.amount and fee_result.amount == '0':
                found_pro_bono_indicator = True
                break  # Pro bono takes highest precedence, stop processing other fee results

        if len(saving_extraction_data.engagement_fee) > 1:
            found_range_amount = True

        if found_pro_bono_indicator:
            is_pro_bono = True
            # All other fields should be set to their default states (None or False)
            engagement_fee_amount = None
            engagement_fee_currency = None
            is_engagement_fee_unknown = False  # It's known to be pro bono
            engagement_fee_display = None
        else:
            # Process other fee types if not pro bono
            for fee_result in saving_extraction_data.engagement_fee:
                # Check for confidential display
                if fee_result.confidential:
                    found_confidential_display = True

                if fee_result.amount:
                    # Try to parse as float for specific amount
                    try:
                        parsed_amount = float(fee_result.amount.replace(',', ''))
                        engagement_fee_amount = parsed_amount
                        engagement_fee_currency = fee_result.currency
                        found_specific_amount = True
                        is_engagement_fee_unknown = False  # It's a known amount
                    except ValueError:
                        # It's a range or non-numeric string
                        found_range_amount = True
                else:
                    # Amount is None, implies missing or unknown
                    found_range_amount = True  # Treat as unknown/range if amount is None

            # Apply specific amount logic
            if found_specific_amount and not found_range_amount:  # Only if *only* specific amounts were found
                is_engagement_fee_unknown = False
                is_pro_bono = False  # Ensure it's false if a specific amount was found
                if not found_confidential_display:
                    engagement_fee_display = EngagementFeeDisplay.ACTUAL_AMOUNT
            elif found_range_amount or not found_specific_amount:  # If range found, or no specific amount found
                is_engagement_fee_unknown = True
                engagement_fee_amount = None
                engagement_fee_currency = None
                is_pro_bono = False  # Ensure it's false if a range/unknown was found

            if found_range_amount:
                engagement_fee_display = EngagementFeeDisplay.FEE_RANGE
            # Apply confidential display logic (overrides "Actual Amount" if both present)
            if found_confidential_display:
                engagement_fee_display = EngagementFeeDisplay.CONFIDENTIAL

    # Handle missing Engagement Fee information (if list is empty or no valid data found)
    if not found_any_fee_info and not found_pro_bono_indicator:
        is_engagement_fee_unknown = True
        engagement_fee_amount = None
        engagement_fee_currency = None
        is_pro_bono = False
        engagement_fee_display = None  # No display value if completely missing

    logger.info(f'Engagement title: {saving_extraction_data.engagement_title}')
    logger.info(f'Engagements fees: {saving_extraction_data.engagement_fee}')
    logger.info(f'Engagements business_issues: {saving_extraction_data.business_issues}')
    logger.info(f'Engagements scope_approach: {saving_extraction_data.scope_approach}')
    logger.info(f'Engagements value_delivered: {saving_extraction_data.value_delivered}')
    logger.info(f'Engagements engagement_summary: {saving_extraction_data.engagement_summary}')
    logger.info(f'Engagements one_line_description: {saving_extraction_data.one_line_description}')
    logger.info(f'Engagements engagement_locations: {saving_extraction_data.engagement_locations}')
    logger.info(f'Engagements source_of_work: {saving_extraction_data.source_of_work}')

    # Combine client industries values with api data
    client_industries_blob_repo = BlobStorageHelper(
        connection_string=settings.BLOB_STORAGE_SETTINGS.CONNECTION_STRING,
        container_name=settings.BLOB_STORAGE_SETTINGS.DEFAULT_CONTAINER_NAME,
    )
    client_industries_data: dict[str, Any] = client_industries_blob_repo.download_json('client-industries.json')
    extracted_industries = []

    if merged_data.client_industries:
        for client_industry in merged_data.client_industries:
            for ldmf_country, industries in client_industries_data.items():
                if client_industry in industries:
                    extracted_industries.append(
                        IndustryData(
                            name=client_industry,
                            ldmf_country=ldmf_country,
                            is_primary=False,
                        )
                    )
                    break

    # Combine client services values with api data
    client_services_blob_repo = BlobStorageHelper(
        connection_string=settings.BLOB_STORAGE_SETTINGS.CONNECTION_STRING,
        container_name=settings.BLOB_STORAGE_SETTINGS.DEFAULT_CONTAINER_NAME,
    )
    client_services_data: dict[str, Any] = client_services_blob_repo.download_json('client-services.json')

    extracted_services = [
        ServiceData(
            name=service,
            is_primary=False,
        )
        for service in merged_data.client_services or []
        if service in client_services_data.keys()
    ]

    return ExtractedData(
        ConversationPublicId=conversation_id,
        DataSourceType=data_source_type,
        ClientName=client_name,
        LDMFCountry=ldmf_country,
        StartDate=start_date,
        EndDate=end_date,
        StartDateOriginal=start_date_original,
        EndDateOriginal=end_date_original,
        MultipleDates=more_than_two_dates,
        ObjectiveAndScope=merged_data.objective_and_scope,
        Outcomes=merged_data.outcomes,
        CreatedAt=datetime.now(timezone.utc),
        Title=saving_extraction_data.engagement_title,
        BusinessIssues=saving_extraction_data.business_issues,
        ScopeApproach=saving_extraction_data.scope_approach,
        SourceOfWork=saving_extraction_data.source_of_work,
        ValueDelivered=saving_extraction_data.value_delivered,
        EngagementSummary=saving_extraction_data.engagement_summary,
        OneLineDescription=saving_extraction_data.one_line_description,
        ClientReferences=saving_extraction_data.client_references,
        ClientNameSharing=saving_extraction_data.client_name_sharing,
        QualUsage=merged_data.qual_usage,
        TeamRoles=team_roles,
        EngagementFee=engagement_fee_amount,
        EngagementFeeCurrency=engagement_fee_currency,
        IsEngagementFeeUnknown=is_engagement_fee_unknown,
        IsProBono=is_pro_bono,
        EngagementFeeDisplay=engagement_fee_display,
        ClientIndustries=extracted_industries,
        ClientServices=extracted_services,
        EngagementLocations=engagement_locations,
        Other=merged_data.other,
    )


@bp.activity_trigger('saving', ActivityName.SaveExtractionData)
@activity_logging_decorator
async def save_extraction_data(saving: SaveExtractionDataActivityInput):
    """
    Activity function to save the extraction data to the database.
    Implements proper field-specific merging strategies to avoid overriding existing data.
    """
    logger.info(f'Saving extraction data: {saving}')
    try:
        async with cast(AsyncSession, async_session_local()) as session:
            extracted_data_repo = ExtractedDataRepository(session, ConversationRepository(session))
            message_repo = ConversationMessageRepository(session, ConversationRepository(session))

            message_id = UUID(saving.message_id)
            logger.info(f'Message ID: {message_id}')
            message = await message_repo.get(message_id)
            if not message:
                logger.warning(f'Message not found for message_id: {message_id}')
                raise ValueError(f'Message not found for message_id: {message_id}')

            # Check if new data would override confirmed fields
            confirmed_data = await extracted_data_repo.conversation_repository.get_confirmed_data(
                message.conversation_id
            )
            replaced_confirmed_fields = check_confirmed_fields_replacement(
                current_confirmed_data=confirmed_data,
                new_confirmed_data_to_update=NewConfirmedDataForUpdate(
                    client_names=saving.extraction_data.client_names or [],
                    ldmf_countries=saving.extraction_data.lead_member_countries or [],
                    objective_and_scope=saving.extraction_data.objective_and_scope,
                    outcomes=saving.extraction_data.outcomes,
                ),
            )
            if replaced_confirmed_fields:
                return SaveExtractionDataActivityOutput(
                    extracted_data_was_updated=False,
                    replaced_confirmed_fields=replaced_confirmed_fields,
                )

            # Get existing data to merge with new data
            previous_extracted_data = await extracted_data_repo.get(message.conversation_id, saving.data_source_type)

            # Apply field-specific merging strategies
            merged_data = _merge_with_existing_data(saving.extraction_data, previous_extracted_data)

            extracted_data = _prepare_data_for_update(
                conversation_id=message.conversation_id,
                data_source_type=saving.data_source_type,
                merged_data=merged_data,
                saving_extraction_data=saving.extraction_data,
                previous_extracted_data=previous_extracted_data,
            )

            await extracted_data_repo.update(extracted_data)
            await session.commit()

            logger.info(
                f'Successfully saved merged extraction data with {len(merged_data.client_names or [])} client names'
            )
            return SaveExtractionDataActivityOutput(
                extracted_data_was_updated=True,
                replaced_confirmed_fields=None,
            )

    except Exception:
        logger.exception('Error saving extraction data')
        raise


@bp.activity_trigger('aggregation', ActivityName.AggregateMultiSourceData)
@activity_logging_decorator
async def aggregate_multi_source_data(aggregation: AggregateMultiSourceDataActivityInput) -> FinalExtractionDataResults:
    """
    Activity function to aggregate extraction data from multiple sources.
    """
    logger.info(f'Aggregating multi-source data: {aggregation}')
    try:
        async with cast(AsyncSession, async_session_local()) as session:
            extracted_data_repo = ExtractedDataRepository(session, ConversationRepository(session))
            message_repo = ConversationMessageRepository(session, ConversationRepository(session))

            # Get message to find conversation_id
            message = await message_repo.get(UUID(aggregation.message_id))
            if not message:
                raise ValueError(f'Message not found: {aggregation.message_id}')

            # Retrieve extraction results from all sources
            source_results = []
            for source_type, _ in aggregation.source_results:
                extracted_data = await extracted_data_repo.get(
                    conversation_id=message.conversation_id, data_source_type=source_type
                )
                if extracted_data:
                    # Convert to FinalExtractionDataResults format
                    periods = None
                    if extracted_data.start_date or extracted_data.end_date:
                        periods = [
                            EngagementPeriod(
                                start_date=extracted_data.start_date.isoformat() if extracted_data.start_date else None,
                                end_date=extracted_data.end_date.isoformat() if extracted_data.end_date else None,
                            )
                        ]

                    # get original periods
                    periods_original = None
                    if extracted_data.start_date_original or extracted_data.end_date_original:
                        periods_original = [
                            EngagementPeriod(
                                start_date=extracted_data.start_date_original
                                if extracted_data.start_date_original
                                else None,
                                end_date=extracted_data.end_date_original if extracted_data.end_date_original else None,
                            )
                        ]

                    final_result = FinalExtractionDataResults(
                        client_names=extracted_data.client_name if isinstance(extracted_data.client_name, list) else [],
                        lead_member_countries=extracted_data.ldmf_country
                        if isinstance(extracted_data.ldmf_country, list)
                        else [],
                        engagement_title=extracted_data.title,
                        periods=periods,
                        periods_original=periods_original,
                        objective_and_scope=extracted_data.objective_and_scope,
                        outcomes=extracted_data.outcomes,
                        business_issues=extracted_data.business_issues,
                        scope_approach=extracted_data.scope_approach,
                        value_delivered=extracted_data.value_delivered,
                        source_of_work=extracted_data.source_of_work,
                        engagement_summary=extracted_data.engagement_summary,
                        one_line_description=extracted_data.one_line_description,
                    )
                    source_results.append((source_type, final_result))

            # Aggregate results using the enhanced merger
            aggregated_results = ExtractedDataMerger.merge_multi_source_results(source_results)

            logger.info(f'Aggregated results: {aggregated_results}')

            await session.commit()
            return aggregated_results

    except Exception:
        logger.exception('Error aggregating multi-source data')
        raise


@bp.activity_trigger('blob_save', ActivityName.SaveAggregatedResultsToBlob)
@activity_logging_decorator
async def save_aggregated_results_to_blob(blob_save: SaveAggregatedResultsToBlobActivityInput) -> str:
    """
    Activity function to save aggregated results to blob storage.
    """
    logger.info(f'Saving aggregated results to blob: {blob_save}')
    try:
        blob_helper = BlobStorageHelper()

        # Create blob path for aggregated results
        blob_path = f'aggregated/{blob_save.message_id}/final_results.json'

        # Save aggregated data to blob storage
        blob_url = blob_helper.upload_json(blob_path, blob_save.aggregated_data.model_dump())

        logger.info(f'Saved aggregated results to blob: {blob_url}')
        return blob_url

    except Exception:
        logger.exception('Error saving aggregated results to blob')
        raise


@bp.activity_trigger('final_queue', ActivityName.SendFinalQueueMessage)
@activity_logging_decorator
async def send_final_queue_message(final_queue: SendFinalQueueMessageActivityInput):
    """
    Activity function to send final queue message for further processing.
    """
    logger.info(f'Sending final queue message: {final_queue}')
    try:
        # Send message to the extracted queue for further processing
        queue_repo = DocumentQueueRepository(
            settings.QUEUE_SETTINGS.CONNECTION_STRING, settings.QUEUE_SETTINGS.CONTENT_PROCESSING_QUEUE_EXTRACTED
        )

        message_data = {
            'message_id': final_queue.message_id,
            'blob_url': final_queue.blob_url,
            'signalr_user_id': final_queue.signalr_user_id,
            'processing_type': 'unified_aggregated',
        }

        await queue_repo._send_message(message_data)

        logger.info(f'Sent final queue message for message_id: {final_queue.message_id}')

    except Exception:
        logger.exception('Error sending final queue message')
        raise


@bp.activity_trigger('blob_save', ActivityName.SaveExtractionResultsToBlob)
@activity_logging_decorator
async def save_extraction_results_to_blob(blob_save: SaveExtractionResultsToBlobActivityInput) -> str:
    """
    Activity function to save extraction results to blob storage.
    """
    logger.info(f'Saving extraction results to blob: {blob_save}')
    try:
        blob_helper = BlobStorageHelper()

        # Create blob path for extraction results
        blob_path = f'chunks_extracted/{blob_save.message_id}/extract_{blob_save.source}_results.json'

        chunks_dict = {'chunks': [chunk.model_dump() for chunk in blob_save.extract_data_results]}
        # Save extraction data to blob storage
        blob_url = blob_helper.upload_json(blob_path, chunks_dict)

        logger.info(f'Saved extraction results to blob: {blob_url}')
        return blob_url

    except Exception:
        logger.exception('Error saving extraction results to blob')
        raise


@bp.activity_trigger('list_blobs_input', ActivityName.ListBlobs)
@activity_logging_decorator
async def list_blobs(list_blobs_input: ListBlobsActivityInput) -> list[ListBlobsActivityOutput]:
    """
    Activity function to list blobs in blob storage and return their content.
    """
    logger.info(f'Listing blobs with prefix: {list_blobs_input.prefix}')
    try:
        blob_helper = BlobStorageHelper()
        blob_names = blob_helper.list_blobs(prefix=list_blobs_input.prefix)
        logger.info(f'Found {len(blob_names)} blobs with prefix: {list_blobs_input.prefix}')

        if not blob_names:
            return []

        # Compile the regex once outside the loop
        source_regex = re.compile(r'extract_(.+)_results.json')

        outputs = []
        for blob_name in blob_names:
            try:
                # extract source from blob name
                match = source_regex.search(blob_name)
                if not match:
                    logger.warning(f'Could not extract source from blob name: {blob_name}')
                    continue
                source = match.group(1)
                blob_dict = blob_helper.download_json(blob_name)
                chunks = [LLMExtractedDataResult.model_validate(chunk) for chunk in blob_dict['chunks']]
                outputs.append(ListBlobsActivityOutput(source=source, chunks=chunks))
            except Exception as e:
                logger.warning(f'Could not read blob {blob_name}. Error: {e}')
        return outputs

    except Exception:
        logger.exception('Error listing blobs')
        raise
